version: '3.8'

services:
  # MySQL Database Service
  db:
    image: mysql:8.0
    container_name: chat_agent_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: std_db
      MYSQL_USER: develorian
      MYSQL_PASSWORD: vr41nr0t410
      MYSQL_ROOT_PASSWORD: root_password_123
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    networks:
      - chat_agent_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 10s
      retries: 5
      interval: 10s

  # Django Web Application Service  
  web:
    build: .
    container_name: chat_agent_web
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Database Configuration
      DB_ENGINE: django.db.backends.mysql
      DB_NAME: std_db
      DB_USER: develorian
      DB_PASSWORD: vr41nr0t410
      DB_HOST: db
      DB_PORT: 3306
      
      # Django Configuration
      DEBUG: "True"
      SECRET_KEY: "django-insecure-docker-key-change-in-production"
      ALLOWED_HOSTS: "localhost,127.0.0.1,0.0.0.0,web"
      
      # LLM API Keys (override with your actual keys)
      XAI_API_KEY: ${XAI_API_KEY:-}
      GROK_API_KEY: ${GROK_API_KEY:-}
      GROQ_API_KEY: ${GROQ_API_KEY:-}
      GEMINI_API_KEY: ${GEMINI_API_KEY:-AIzaSyBkpsKUJ5S51OA2Ml6b98rvRwdAbJXK6Y0}
      MISTRAL_API_KEY: ${MISTRAL_API_KEY:-qySRKGu53jjrKjmuZoicrGbpoS1gej0f}
      
      # LLM Configuration
      ACTIVE_LLM_PROVIDER: grok
      ACTIVE_GEMINI_MODEL: gemini-1.5-pro
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
      - ./:/app
    depends_on:
      db:
        condition: service_healthy
    networks:
      - chat_agent_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/chat/health/"]
      timeout: 10s
      retries: 5
      interval: 30s

  # Optional: Nginx reverse proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: chat_agent_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf:ro
      - static_volume:/app/staticfiles:ro
      - media_volume:/app/media:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - web
    networks:
      - chat_agent_network
    profiles:
      - production

# Named volumes for persistent data
volumes:
  mysql_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  logs_volume:
    driver: local

# Network for inter-service communication
networks:
  chat_agent_network:
    driver: bridge 