#!/bin/bash

# Exit on any error
set -e

echo "Starting Chat Agent Application in DEVELOPMENT mode..."

# Function to wait for MySQL
wait_for_mysql() {
    echo "Waiting for MySQL to be ready..."
    while ! nc -z "$DB_HOST" "$DB_PORT"; do
        echo "MySQL is unavailable - sleeping"
        sleep 2
    done
    echo "MySQL is up - continuing..."
}

# Wait for database if DB_HOST is set
if [ "$DB_HOST" ]; then
    wait_for_mysql
fi

# Install/update dependencies if requirements.txt changed
echo "Checking for new dependencies..."
pip install -r requirements.txt

# Run database migrations
echo "Running database migrations..."
python manage.py makemigrations --noinput
python manage.py migrate --noinput

# Create superuser if it doesn't exist (development)
echo "Checking for development superuser..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='dev').exists():
    User.objects.create_superuser('dev', '<EMAIL>', 'dev123')
    print("Development superuser 'dev' created with password 'dev123'")
else:
    print("Development superuser 'dev' already exists")
EOF

# Load development fixtures if they exist
if [ -f "chat/fixtures/dev_data.json" ]; then
    echo "Loading development fixtures..."
    python manage.py loaddata chat/fixtures/dev_data.json
fi

# Collect static files (development)
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Show development information
echo "=========================================="
echo "🚀 DEVELOPMENT ENVIRONMENT READY"
echo "=========================================="
echo "Web Application: http://localhost:8001"
echo "Admin Panel: http://localhost:8001/admin/"
echo "Development User: dev / dev123"
echo "=========================================="

# Start the development server with auto-reload
echo "Starting Django development server with auto-reload..."
exec python manage.py runserver 0.0.0.0:8000 