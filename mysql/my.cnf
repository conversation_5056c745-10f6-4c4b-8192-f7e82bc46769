[mysqld]
# Character set and collation
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default_authentication_plugin = mysql_native_password

# Performance settings
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# Connection settings
max_connections = 100
max_allowed_packet = 64M
wait_timeout = 600
interactive_timeout = 600

# Binary logging (for replication if needed)
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7

# Error logging
log-error = /var/log/mysql/error.log

# Slow query logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# General settings
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4 