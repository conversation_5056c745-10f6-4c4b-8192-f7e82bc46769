-- Initialize the Chat Agent database
CREATE DATABASE IF NOT EXISTS std_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

CREATE DATABASE IF NOT EXISTS std_db_dev 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Grant privileges to the application user
GRANT ALL PRIVILEGES ON std_db.* TO 'develorian'@'%';
GRANT ALL PRIVILEGES ON std_db_dev.* TO 'develorian'@'%';

-- Create additional user for backup/admin tasks
CREATE USER IF NOT EXISTS 'chat_admin'@'%' IDENTIFIED BY 'admin_password_123';
GRANT ALL PRIVILEGES ON std_db.* TO 'chat_admin'@'%';
GRANT ALL PRIVILEGES ON std_db_dev.* TO 'chat_admin'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Show created databases
SHOW DATABASES; 