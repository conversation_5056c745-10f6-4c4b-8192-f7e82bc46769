version: '3.8'

services:
  # MySQL Database Service (same as production)
  db:
    image: mysql:8.0
    container_name: chat_agent_mysql_dev
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: std_db_dev
      MYSQL_USER: develorian
      MYSQL_PASSWORD: vr41nr0t410
      MYSQL_ROOT_PASSWORD: root_password_123
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3307:3306"  # Different port to avoid conflicts
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    networks:
      - chat_agent_dev_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 10s
      retries: 5
      interval: 10s

  # Django Development Service (with hot reloading)
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: chat_agent_web_dev
    restart: unless-stopped
    ports:
      - "8001:8000"  # Different port for development
    environment:
      # Database Configuration
      DB_ENGINE: django.db.backends.mysql
      DB_NAME: std_db_dev
      DB_USER: develorian
      DB_PASSWORD: vr41nr0t410
      DB_HOST: db
      DB_PORT: 3306
      
      # Django Development Configuration
      DEBUG: "True"
      SECRET_KEY: "django-insecure-development-key-only"
      ALLOWED_HOSTS: "localhost,127.0.0.1,0.0.0.0,web"
      
      # LLM API Keys (load from .env file)
      XAI_API_KEY: ${XAI_API_KEY:-}
      GROK_API_KEY: ${GROK_API_KEY:-}
      GROQ_API_KEY: ${GROQ_API_KEY:-}
      GEMINI_API_KEY: ${GEMINI_API_KEY:-}
      MISTRAL_API_KEY: ${MISTRAL_API_KEY:-}
      
      # Development LLM Configuration
      ACTIVE_LLM_PROVIDER: groq  # Use faster provider for development
      ACTIVE_GEMINI_MODEL: gemini-1.5-flash
    volumes:
      # Mount source code for hot reloading
      - ./:/app
      - /app/node_modules  # Exclude node_modules
      - dev_static_volume:/app/staticfiles
      - dev_media_volume:/app/media
      - dev_logs_volume:/app/logs
    depends_on:
      db:
        condition: service_healthy
    networks:
      - chat_agent_dev_network
    command: python manage.py runserver 0.0.0.0:8000
    stdin_open: true
    tty: true

  # Redis for development (optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: chat_agent_redis_dev
    restart: unless-stopped
    ports:
      - "6380:6379"  # Different port for development
    volumes:
      - redis_dev_data:/data
    networks:
      - chat_agent_dev_network
    profiles:
      - with-redis

  # Mailhog for development email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: chat_agent_mailhog_dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web interface
    networks:
      - chat_agent_dev_network
    profiles:
      - with-email

# Named volumes for development data
volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  dev_static_volume:
    driver: local
  dev_media_volume:
    driver: local
  dev_logs_volume:
    driver: local

# Development network
networks:
  chat_agent_dev_network:
    driver: bridge 