# Chat Agent Docker Setup 🐳

This document provides comprehensive instructions for running the Chat Agent application using Docker and Docker Compose.

## Prerequisites

- Docker Engine 20.10+ 
- Docker Compose 2.0+
- Git

## Quick Start

### 1. Clone and Setup Environment

```bash
# Clone the repository
git clone <your-repo-url>
cd chat-agent

# Copy environment file and configure
cp env.example .env
# Edit .env with your actual API keys
```

### 2. Run Complete Application

```bash
# Start web app + database
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

**Access Points:**
- **Web App**: http://localhost:8000
- **Admin Panel**: http://localhost:8000/admin/
- **Login**: admin / admin123

## Available Docker Compose Configurations

### 1. **Production Setup** (`docker-compose.yml`)
```bash
# Start complete stack (web + db + nginx)
docker-compose up -d

# With Nginx reverse proxy
docker-compose --profile production up -d
```

### 2. **Development Setup** (`docker-compose.dev.yml`)
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# With additional services (Redis, Mailhog)
docker-compose -f docker-compose.dev.yml --profile with-redis --profile with-email up -d
```

**Development Features:**
- Hot reloading
- Development tools (ipython, django-debug-toolbar)
- Separate dev database
- Port 8001 (to avoid conflicts)
- Dev user: dev / dev123

### 3. **Database Only** (`docker-compose.db-only.yml`)
```bash
# MySQL database only
docker-compose -f docker-compose.db-only.yml up -d

# With phpMyAdmin
docker-compose -f docker-compose.db-only.yml --profile with-admin up -d
```

## Environment Configuration

### Required Environment Variables

Create a `.env` file with your configuration:

```bash
# Database
DB_NAME=std_db
DB_USER=develorian
DB_PASSWORD=vr41nr0t410
DB_HOST=db

# LLM API Keys (required)
XAI_API_KEY=your-xai-key
GROK_API_KEY=your-grok-key
GROQ_API_KEY=your-groq-key
GEMINI_API_KEY=your-gemini-key
MISTRAL_API_KEY=your-mistral-key

# Django
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1
```

## Service Details

### Web Application (`web`)
- **Technology**: Django 5.1+ with Python 3.11
- **Port**: 8000 (production), 8001 (development)
- **Features**: LangGraph-based LLM providers, multi-agent system
- **Health Check**: `/chat/health/`

### Database (`db`)
- **Technology**: MySQL 8.0
- **Port**: 3306 (production), 3307 (development)
- **Character Set**: UTF8MB4
- **Persistent Storage**: Named volumes

### Nginx (`nginx`) - Production Only
- **Port**: 80 (HTTP), 443 (HTTPS)
- **Features**: Static file serving, gzip compression, security headers
- **SSL**: Configure certificates in `nginx/ssl/`

## Common Commands

### Application Management
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart web

# View real-time logs
docker-compose logs -f web

# Execute commands in running container
docker-compose exec web python manage.py shell
docker-compose exec web python manage.py createsuperuser
```

### Database Management
```bash
# Connect to MySQL
docker-compose exec db mysql -u root -p std_db

# Run migrations
docker-compose exec web python manage.py migrate

# Create database backup
docker-compose exec db mysqldump -u root -p std_db > backup.sql

# Restore from backup
docker-compose exec -T db mysql -u root -p std_db < backup.sql
```

### Development Workflow
```bash
# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Install new dependencies
docker-compose -f docker-compose.dev.yml exec web pip install new-package
docker-compose -f docker-compose.dev.yml restart web

# Run tests
docker-compose -f docker-compose.dev.yml exec web python manage.py test

# Django shell
docker-compose -f docker-compose.dev.yml exec web python manage.py shell_plus
```

## Volume Management

### Persistent Data Locations
- **Database**: `mysql_data` volume
- **Static Files**: `static_volume` volume  
- **Media Files**: `media_volume` volume
- **Logs**: `logs_volume` volume

### Backup & Restore
```bash
# Create volume backup
docker run --rm -v mysql_data:/data -v $(pwd):/backup busybox tar czf /backup/mysql_backup.tar.gz /data

# Restore volume
docker run --rm -v mysql_data:/data -v $(pwd):/backup busybox tar xzf /backup/mysql_backup.tar.gz -C /
```

## Troubleshooting

### Common Issues

#### 1. **Database Connection Errors**
```bash
# Check database status
docker-compose ps db
docker-compose logs db

# Wait for database to be ready
docker-compose exec web python manage.py migrate
```

#### 2. **Permission Issues**
```bash
# Fix file permissions
sudo chown -R $(id -u):$(id -g) .

# Fix volume permissions
docker-compose exec web chown -R www-data:www-data /app/media /app/staticfiles
```

#### 3. **Port Conflicts**
```bash
# Check what's using the port
sudo netstat -tulpn | grep :8000

# Kill process using port
sudo kill -9 $(sudo lsof -t -i:8000)
```

#### 4. **API Key Issues**
```bash
# Check environment variables
docker-compose exec web env | grep API_KEY

# Test LLM connections
docker-compose exec web python manage.py shell -c "from chat.llm_providers.factory import LLMProviderFactory; print(LLMProviderFactory.test_providers())"
```

### Performance Optimization

#### Production Settings
```bash
# Use production compose with Nginx
docker-compose --profile production up -d

# Optimize database
docker-compose exec db mysql -u root -p -e "OPTIMIZE TABLE std_db.*;"

# Monitor resource usage
docker stats
```

## Security Considerations

### Production Checklist
- [ ] Change default passwords in `.env`
- [ ] Set `DEBUG=False` in production
- [ ] Configure proper `SECRET_KEY`
- [ ] Set up SSL certificates
- [ ] Configure firewall rules
- [ ] Enable database backups
- [ ] Set up log rotation

### API Key Security
- Use environment variables only
- Never commit API keys to git
- Rotate keys regularly
- Monitor API usage

## Monitoring & Logs

### Log Locations
```bash
# Application logs
docker-compose logs web

# Database logs  
docker-compose logs db

# Nginx logs
docker-compose logs nginx

# Follow all logs
docker-compose logs -f
```

### Health Checks
```bash
# Check application health
curl http://localhost:8000/chat/health/

# Check database health
docker-compose exec db mysqladmin ping

# Service status
docker-compose ps
```

## Development Tips

### Hot Reloading
Development containers mount source code, so changes are reflected immediately without rebuilding.

### Database Migrations
```bash
# Create migrations
docker-compose exec web python manage.py makemigrations

# Apply migrations
docker-compose exec web python manage.py migrate

# Reset database (development only)
docker-compose exec web python manage.py flush
```

### Testing New LLM Providers
```bash
# Test new graph provider
docker-compose exec web python manage.py shell -c "
from chat.graphs.graph_factory import GraphLLMFactory
provider = GraphLLMFactory.get_provider('groq')
print(provider.get_model_info())
"
```

## Support

For issues related to:
- **Docker Setup**: Check this documentation
- **LLM Providers**: Check `chat/graphs/` implementation
- **Database**: Check MySQL logs and configuration
- **Application**: Check Django logs and settings

## License

This Docker configuration is part of the Chat Agent project. 