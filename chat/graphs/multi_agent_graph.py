"""
Multi-Agent Graph implementation using LangGraph.
Replicates the functionality of the existing Multi-Agent provider using LangGraph framework.
"""
import logging
from typing import Dict, Any, Optional
from django.conf import settings

from langchain_core.messages import HumanMessage, AIMessage
from langchain.chains.conversation.memory import ConversationBufferWindowMemory

from .base_llm_graph import Base<PERSON>MGraph, LLMGraphState
from .grok_graph import GrokGraph
from .groq_graph import GroqGraph

logger = logging.getLogger(__name__)

class MultiAgentGraph(BaseLLMGraph):
    """
    LangGraph implementation of the Multi-Agent provider.
    Replicates all functionality from the original MultiAgentProvider.
    """
    
    # Available "models" for multi-agent (replicates original)
    AVAILABLE_MODELS = {
        'multi-agent-study': {
            'id': 'multi-agent-study',
            'capabilities': ['text', 'code', 'reasoning', 'multi-agent'],
            'max_tokens': 8192,
            'description': 'Sistema multi-agente para estudio y análisis complejo'
        }
    }
    
    def __init__(self, model_name: Optional[str] = None):
        """Initialize the Multi-Agent graph."""
        # Initialize provider settings before calling parent
        self._initialize_provider_settings()
        super().__init__(model_name)
        
        # Initialize agent instances (replicates original)
        self.groq_agent = None
        self.grok_agent = None
        self._initialize_agents()
    
    def _get_default_model(self) -> str:
        """Get the default Multi-Agent model."""
        return "multi-agent-study"
    
    def _initialize_provider_settings(self) -> None:
        """Initialize Multi-Agent-specific settings."""
        # Multi-agent doesn't need its own API key, uses sub-agents
        self.api_key = "multi-agent"  # Placeholder
        self.temperature = 0.7
    
    def _initialize_agents(self) -> None:
        """Initialize the sub-agents (replicates original approach)."""
        try:
            # Initialize Groq for fast classification/routing
            self.groq_agent = GroqGraph(model_name="deepseek-r1-distill-llama-70b")
            
            # Initialize Grok for detailed analysis
            self.grok_agent = GrokGraph(model_name="grok-3-beta")
            
            logger.info("Multi-agent sub-agents initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing multi-agent sub-agents: {e}")
            self.groq_agent = None
            self.grok_agent = None
    
    def _call_llm(self, state: LLMGraphState) -> LLMGraphState:
        """Coordinate multiple agents to process the query."""
        try:
            state["api_call_count"] += 1
            
            # Determine which agents to use based on query analysis
            selected_agents = self._select_agents(state["user_input"])
            
            self.logger.info(f"Selected agents: {selected_agents} for multi-agent processing")
            
            responses = []
            agent_errors = []
            
            # Process with each selected agent
            for agent_name in selected_agents:
                try:
                    if agent_name in self.agents:
                        agent = self.agents[agent_name]
                        
                        # Create a copy of state for this agent
                        agent_state = state.copy()
                        
                        # Get response from this agent
                        response = agent.query(
                            user_input=state["user_input"],
                            memory_instance=None  # Multi-agent manages its own context
                        )
                        
                        if response and not response.startswith("Error:"):
                            responses.append({
                                "agent": agent_name,
                                "response": response
                            })
                        else:
                            agent_errors.append(f"{agent_name}: {response}")
                            
                except Exception as e:
                    self.logger.error(f"Error with {agent_name} agent: {e}")
                    agent_errors.append(f"{agent_name}: Error - {str(e)}")
            
            # Process the collected responses
            if responses:
                final_response = self._synthesize_responses(responses, state["user_input"])
                state["response"] = final_response
                state["metadata"]["agents_used"] = [r["agent"] for r in responses]
                state["metadata"]["successful_agents"] = len(responses)
                state["metadata"]["failed_agents"] = len(agent_errors)
                
                if agent_errors:
                    state["metadata"]["agent_errors"] = agent_errors
                
                self.logger.info(f"Multi-agent processing completed with {len(responses)} successful responses")
            else:
                # All agents failed
                error_msg = "All agents failed: " + "; ".join(agent_errors)
                state["error"] = f"multi_agent_failure: {error_msg}"
                self.logger.error(f"Multi-agent processing failed: {error_msg}")
            
            return state
            
        except Exception as e:
            self.logger.error(f"Error in multi-agent coordination: {e}")
            state["error"] = f"coordination_error: {str(e)}"
            return state
    
    def _create_temp_memory(self, messages):
        """Create temporary memory from messages for sub-agents."""
        temp_memory = ConversationBufferWindowMemory(k=5)
        
        # Add messages to temporary memory (skip the last one as it's current input)
        for message in messages[:-1]:
            if isinstance(message, HumanMessage):
                temp_memory.chat_memory.add_user_message(message.content)
            elif isinstance(message, AIMessage):
                temp_memory.chat_memory.add_ai_message(message.content)
        
        return temp_memory
    
    def _enhance_complex_response(self, response: str, user_input: str) -> str:
        """Enhance complex responses with additional formatting."""
        # Add structure for complex responses
        enhanced = f"## Análisis Completo\n\n{response}"
        
        # Add summary if response is very long
        if len(response) > 1000:
            enhanced += "\n\n---\n**Resumen:** Esta respuesta aborda tu consulta con un análisis detallado utilizando nuestro sistema multi-agente."
        
        return enhanced
    
    def _handle_error(self, state: LLMGraphState) -> LLMGraphState:
        """Handle Multi-Agent-specific errors."""
        error_msg = state.get("error", "Unknown error")
        
        # Handle specific error types
        if error_msg.startswith("multi_agent_error"):
            response = f"Error en el sistema multi-agente: {error_msg.replace('multi_agent_error: ', '')}"
        elif "Sub-agent error" in error_msg:
            response = f"Error en el procesamiento: {error_msg}"
        elif "not initialized" in error_msg:
            response = "Error: El sistema multi-agente no está configurado correctamente. Por favor, contacta al administrador del sistema."
        else:
            # Fallback to base implementation
            return super()._handle_error(state)
        
        state["response"] = response
        state["metadata"]["error_handled"] = True
        state["metadata"]["error_type"] = error_msg
        
        logger.warning(f"Multi-agent error handled: {error_msg}")
        return state
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the multi-agent system."""
        base_info = super().get_model_info()
        
        # Add multi-agent specific information
        base_info.update({
            "sub_agents": {
                "groq": self.groq_agent.model_name if self.groq_agent else "not_initialized",
                "grok": self.grok_agent.model_name if self.grok_agent else "not_initialized"
            },
            "routing_strategy": "classification_based",
            "description": "Sistema que combina múltiples agentes para respuestas optimizadas"
        })
        
        return base_info 