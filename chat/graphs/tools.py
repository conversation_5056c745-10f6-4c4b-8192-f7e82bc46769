"""
LangGraph Tools for Chat Agent
Tools that can be bound to LLM graphs for enhanced functionality.
"""
import json
import logging
from typing import Dict, Any, Optional

from langchain_core.tools import tool
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

class ExamGenerationInput(BaseModel):
    """Input schema for exam generation tool."""
    topic: str = Field(description="The topic or subject to generate an exam about")
    num_questions: Optional[int] = Field(default=5, description="Number of questions to generate (default: 5)")
    difficulty: Optional[str] = Field(default="intermediate", description="Difficulty level: easy, intermediate, or advanced")

@tool
def generate_exam(topic: str, num_questions: int = 5, difficulty: str = "medium") -> dict:
    """
    Generate an interactive educational assessment with multiple choice questions.
    
    Use this tool when users want to:
    - Test their knowledge on a subject
    - Create educational quizzes or exams  
    - Generate practice questions
    - Check understanding of a topic
    - Create assessments for learning
    
    Args:
        topic: The subject or topic for the assessment (e.g. "History", "Biology", "Programming")
        num_questions: Number of questions to include (default: 5)
        difficulty: Difficulty level - 'easy', 'medium', or 'hard' (default: 'medium')
        
    Returns:
        dict: Complete interactive exam JSON with questions, options, and explanations
    """
    try:
        import json
        from django.conf import settings
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.messages import HumanMessage, SystemMessage
        from langchain.memory import ConversationBufferWindowMemory
        
        # Create temporary memory instance for tool usage
        temp_memory = ConversationBufferWindowMemory(k=5, memory_key="chat_history", return_messages=True)
        
        # Initialize Gemini LLM for question generation
        try:
            llm = ChatGoogleGenerativeAI(
                model="gemini-2.0-flash-exp",
                temperature=0.7,
                max_tokens=2048,
                google_api_key=settings.GEMINI_API_KEY
            )
        except Exception as e:
            logger.warning(f"Could not initialize Gemini for question generation: {e}")
            # Fall back to predefined questions if LLM fails
            return _create_fallback_exam(topic, num_questions, difficulty)
        
        # System prompt for generating high-quality exam questions
        system_prompt = f"""You are an expert educational content creator specializing in {topic}. Generate a high-quality multiple choice question that tests real knowledge about {topic} at {difficulty} difficulty level.

REQUIREMENTS:
- Question must be specific, factual, and educational about {topic}
- Test actual understanding, not just memorization
- Provide exactly 4 answer options (A, B, C, D)
- Only ONE option should be correct
- Include a detailed explanation for why the correct answer is right
- Make the question appropriate for {difficulty} level
- Use realistic distractors (wrong answers that seem plausible)

DIFFICULTY GUIDELINES:
- Easy: Basic concepts, definitions, simple facts
- Medium: Application of knowledge, connections between concepts
- Hard: Analysis, synthesis, complex scenarios, advanced understanding

Return ONLY a JSON object with this exact structure:
{{
    "question": "Specific, clear question about {topic}",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correct_answer": 0,
    "explanation": "Detailed explanation of why this answer is correct and why others are wrong"
}}"""

        # Generate questions one by one using the LLM
        questions = []
        for i in range(num_questions):
            try:
                # Create specific prompt for this question
                question_prompt = f"""Generate question {i+1} of {num_questions} about {topic} ({difficulty} difficulty).

SPECIFIC REQUIREMENTS FOR THIS QUESTION:
1. Focus on a different aspect of {topic} than previous questions
2. Make it {difficulty} difficulty level
3. Use specific terminology and concepts from {topic}
4. Create realistic but clearly distinguishable options
5. Ensure educational value and practical relevance

TOPIC AREAS TO CONSIDER FOR {topic}:
- Core concepts and fundamental principles
- Key terminology and definitions
- Important facts and historical information
- Practical applications and examples
- Common misconceptions to address
- Cause and effect relationships
- Comparisons and contrasts

QUESTION FORMAT:
- Start with a clear, specific question
- Use proper grammar and professional language
- Make options similar in length and structure
- Avoid obvious giveaways in wording
- Include numbers, dates, or specific examples when relevant

Generate the complete JSON response now:"""

                # Call LLM to generate the question
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=question_prompt)
                ]
                
                response = llm.invoke(messages)
                question_text = response.content.strip()
                
                # Clean and parse JSON response
                if question_text.startswith('```json'):
                    question_text = question_text.replace('```json', '').replace('```', '').strip()
                elif question_text.startswith('```'):
                    question_text = question_text.replace('```', '').strip()
                
                # Parse the JSON
                question_data = json.loads(question_text)
                
                # Validate question structure
                if not all(key in question_data for key in ['question', 'options', 'correct_answer', 'explanation']):
                    raise ValueError("Invalid question structure")
                
                if len(question_data['options']) != 4:
                    raise ValueError("Must have exactly 4 options")
                
                # Add ID and append to questions
                question_data['id'] = f"q{i + 1}"
                questions.append(question_data)
                
                logger.info(f"Generated question {i+1}: {question_data['question'][:50]}...")
                
            except Exception as e:
                logger.error(f"Error generating question {i+1}: {e}")
                # Create a fallback question for this slot
                fallback_question = {
                    "id": f"q{i + 1}",
                    "question": f"What is an important concept to understand about {topic}?",
                    "options": [
                        f"Key concept A related to {topic}",
                        f"Key concept B related to {topic}", 
                        f"Key concept C related to {topic}",
                        f"Key concept D related to {topic}"
                    ],
                    "correct_answer": 0,
                    "explanation": f"This demonstrates important principles in {topic} that are essential for understanding."
                }
                questions.append(fallback_question)
        
        # Calculate time limit based on difficulty and number of questions
        time_per_question = {"easy": 2, "medium": 3, "hard": 4}
        time_limit = num_questions * time_per_question.get(difficulty, 3)
        
        # Create the complete exam structure
        exam_data = {
            "title": f"Examen de {topic.title()}",
            "description": f"Evaluación de {difficulty} nivel sobre {topic} con {num_questions} preguntas generadas por IA",
            "time_limit": time_limit,
            "passing_score": 70,
            "questions": questions
        }
        
        logger.info(f"Successfully generated exam with {len(questions)} LLM-generated questions about {topic}")
        return exam_data
        
    except Exception as e:
        logger.error(f"Error in generate_exam tool: {e}")
        # Return fallback exam in case of any error
        return _create_fallback_exam(topic, num_questions, difficulty)

def _create_fallback_exam(topic: str, num_questions: int, difficulty: str) -> dict:
    """Create a fallback exam when LLM generation fails."""
    questions = []
    for i in range(num_questions):
        question = {
            "id": f"q{i + 1}",
            "question": f"Pregunta {i+1} sobre {topic}: ¿Cuál concepto es más importante?",
            "options": [
                f"Concepto fundamental A de {topic}",
                f"Concepto fundamental B de {topic}",
                f"Concepto fundamental C de {topic}",
                f"Concepto fundamental D de {topic}"
            ],
            "correct_answer": 0,
            "explanation": f"Esta respuesta aborda conceptos clave de {topic} importantes para el aprendizaje."
        }
        questions.append(question)
    
    time_per_question = {"easy": 2, "medium": 3, "hard": 4}
    time_limit = num_questions * time_per_question.get(difficulty, 3)
    
    return {
        "title": f"Examen de {topic.title()} (Fallback)",
        "description": f"Evaluación básica sobre {topic}",
        "time_limit": time_limit,
        "passing_score": 70,
        "questions": questions
    }

def _create_knowledge_based_exam(topic: str, num_questions: int, difficulty: str) -> dict:
    """Create exam questions based on topic knowledge."""
    exam_data = {
        "title": f"{topic.title()} Exam",
        "description": f"Test your knowledge of {topic} with this {difficulty} level exam",
        "questions": []
    }
    
    # Comprehensive topic-specific question pools
    topic_lower = topic.lower()
    
    if "biology" in topic_lower:
        questions_pool = [
            {
                "question_text": "What is the basic unit of life?",
                "options": ["Atom", "Molecule", "Cell", "Tissue"],
                "correct_answer_index": 2,
                "explanation": "The cell is the basic structural and functional unit of all living organisms."
            },
            {
                "question_text": "Which process do plants use to make their own food?",
                "options": ["Respiration", "Photosynthesis", "Digestion", "Circulation"],
                "correct_answer_index": 1,
                "explanation": "Photosynthesis is the process by which plants convert light energy into chemical energy (glucose)."
            },
            {
                "question_text": "What is DNA an abbreviation for?",
                "options": ["Deoxyribonucleic Acid", "Dinitrogen Acid", "Double Nucleic Acid", "Dextrose Nucleic Acid"],
                "correct_answer_index": 0,
                "explanation": "DNA stands for Deoxyribonucleic Acid, which carries genetic information."
            },
            {
                "question_text": "Which organelle is responsible for energy production in cells?",
                "options": ["Nucleus", "Mitochondria", "Ribosome", "Endoplasmic Reticulum"],
                "correct_answer_index": 1,
                "explanation": "Mitochondria are known as the powerhouses of the cell, producing ATP through cellular respiration."
            },
            {
                "question_text": "What type of molecule is hemoglobin?",
                "options": ["Carbohydrate", "Lipid", "Protein", "Nucleic Acid"],
                "correct_answer_index": 2,
                "explanation": "Hemoglobin is a protein that carries oxygen in red blood cells."
            }
        ]
    elif "physics" in topic_lower:
        questions_pool = [
            {
                "question_text": "What is the speed of light in a vacuum?",
                "options": ["3 × 10⁸ m/s", "3 × 10⁶ m/s", "3 × 10¹⁰ m/s", "3 × 10⁴ m/s"],
                "correct_answer_index": 0,
                "explanation": "The speed of light in a vacuum is approximately 3 × 10⁸ meters per second, a fundamental constant in physics."
            },
            {
                "question_text": "Which law states that force equals mass times acceleration?",
                "options": ["Newton's First Law", "Newton's Second Law", "Newton's Third Law", "Law of Conservation of Energy"],
                "correct_answer_index": 1,
                "explanation": "Newton's Second Law states that F = ma, where force equals mass times acceleration."
            },
            {
                "question_text": "What is the unit of electrical resistance?",
                "options": ["Volt", "Ampere", "Ohm", "Watt"],
                "correct_answer_index": 2,
                "explanation": "The ohm (Ω) is the unit of electrical resistance, named after Georg Ohm."
            },
            {
                "question_text": "What type of energy does a moving object possess?",
                "options": ["Potential Energy", "Kinetic Energy", "Thermal Energy", "Chemical Energy"],
                "correct_answer_index": 1,
                "explanation": "Kinetic energy is the energy of motion, calculated as ½mv²."
            },
            {
                "question_text": "What happens to the wavelength of light when its frequency increases?",
                "options": ["It increases", "It decreases", "It stays the same", "It becomes zero"],
                "correct_answer_index": 1,
                "explanation": "According to the wave equation c = λf, when frequency increases, wavelength decreases."
            }
        ]
    elif "history" in topic_lower:
        questions_pool = [
            {
                "question_text": "In which year did World War II end?",
                "options": ["1944", "1945", "1946", "1947"],
                "correct_answer_index": 1,
                "explanation": "World War II ended in 1945 with the surrender of Japan in September."
            },
            {
                "question_text": "Who was the first President of the United States?",
                "options": ["Thomas Jefferson", "John Adams", "George Washington", "Benjamin Franklin"],
                "correct_answer_index": 2,
                "explanation": "George Washington was the first President of the United States, serving from 1789 to 1797."
            },
            {
                "question_text": "Which ancient civilization built the pyramids of Giza?",
                "options": ["Romans", "Greeks", "Egyptians", "Babylonians"],
                "correct_answer_index": 2,
                "explanation": "The ancient Egyptians built the pyramids of Giza around 2580-2510 BCE during the Old Kingdom period."
            },
            {
                "question_text": "The Renaissance period began in which country?",
                "options": ["France", "England", "Germany", "Italy"],
                "correct_answer_index": 3,
                "explanation": "The Renaissance began in Italy in the 14th century, particularly in Florence and other Italian city-states."
            },
            {
                "question_text": "Which empire was ruled by Julius Caesar?",
                "options": ["Greek Empire", "Roman Empire", "Persian Empire", "Byzantine Empire"],
                "correct_answer_index": 1,
                "explanation": "Julius Caesar was a Roman general and statesman who played a critical role in the Roman Republic."
            }
        ]
    elif "chemistry" in topic_lower:
        questions_pool = [
            {
                "question_text": "What is the chemical symbol for gold?",
                "options": ["Go", "Gd", "Au", "Ag"],
                "correct_answer_index": 2,
                "explanation": "Au is the chemical symbol for gold, derived from the Latin word 'aurum'."
            },
            {
                "question_text": "How many electrons does a neutral carbon atom have?",
                "options": ["4", "6", "8", "12"],
                "correct_answer_index": 1,
                "explanation": "A neutral carbon atom has 6 electrons, which equals its atomic number."
            },
            {
                "question_text": "What type of bond forms when electrons are shared between atoms?",
                "options": ["Ionic bond", "Covalent bond", "Metallic bond", "Hydrogen bond"],
                "correct_answer_index": 1,
                "explanation": "Covalent bonds form when atoms share electrons to achieve stable electron configurations."
            },
            {
                "question_text": "What is the pH of pure water at 25°C?",
                "options": ["6", "7", "8", "9"],
                "correct_answer_index": 1,
                "explanation": "Pure water has a pH of 7 at 25°C, making it neutral (neither acidic nor basic)."
            },
            {
                "question_text": "Which gas makes up about 78% of Earth's atmosphere?",
                "options": ["Oxygen", "Carbon Dioxide", "Nitrogen", "Argon"],
                "correct_answer_index": 2,
                "explanation": "Nitrogen (N₂) makes up approximately 78% of Earth's atmosphere."
            }
        ]
    elif "mathematics" in topic_lower or "math" in topic_lower:
        questions_pool = [
            {
                "question_text": "What is the value of π (pi) rounded to two decimal places?",
                "options": ["3.14", "3.15", "3.16", "3.17"],
                "correct_answer_index": 0,
                "explanation": "π (pi) is approximately 3.14159, which rounds to 3.14 when rounded to two decimal places."
            },
            {
                "question_text": "What is the derivative of x²?",
                "options": ["x", "2x", "x²", "2x²"],
                "correct_answer_index": 1,
                "explanation": "Using the power rule, the derivative of x² is 2x."
            },
            {
                "question_text": "In a right triangle, what is the relationship between the sides called?",
                "options": ["Pythagorean Theorem", "Law of Cosines", "Law of Sines", "Triangle Inequality"],
                "correct_answer_index": 0,
                "explanation": "The Pythagorean Theorem states that a² + b² = c² in a right triangle."
            },
            {
                "question_text": "What is the result of 5! (5 factorial)?",
                "options": ["25", "120", "625", "3125"],
                "correct_answer_index": 1,
                "explanation": "5! = 5 × 4 × 3 × 2 × 1 = 120"
            },
            {
                "question_text": "What type of number is √(-1)?",
                "options": ["Real number", "Rational number", "Imaginary number", "Natural number"],
                "correct_answer_index": 2,
                "explanation": "√(-1) is defined as the imaginary unit 'i', making it an imaginary number."
            }
        ]
    elif "python" in topic_lower or "programming" in topic_lower:
        questions_pool = [
            {
                "question_text": "Which of the following is the correct way to create a list in Python?",
                "options": ["list = (1, 2, 3)", "list = {1, 2, 3}", "list = [1, 2, 3]", "list = <1, 2, 3>"],
                "correct_answer_index": 2,
                "explanation": "Square brackets [] are used to create lists in Python."
            },
            {
                "question_text": "What does the 'len()' function do in Python?",
                "options": ["Adds length to a string", "Returns the length of an object", "Creates a new length", "Deletes length"],
                "correct_answer_index": 1,
                "explanation": "The len() function returns the number of items in an object like a list, string, or dictionary."
            },
            {
                "question_text": "Which Python keyword is used to define a function?",
                "options": ["function", "def", "define", "func"],
                "correct_answer_index": 1,
                "explanation": "The 'def' keyword is used to define functions in Python."
            },
            {
                "question_text": "What is the output of print(type([]))?",
                "options": ["<class 'list'>", "<class 'array'>", "<class 'tuple'>", "<class 'dict'>"],
                "correct_answer_index": 0,
                "explanation": "Empty square brackets [] create a list object, so type([]) returns <class 'list'>."
            },
            {
                "question_text": "Which operator is used for exponentiation in Python?",
                "options": ["^", "**", "exp", "pow"],
                "correct_answer_index": 1,
                "explanation": "The ** operator is used for exponentiation in Python (e.g., 2**3 = 8)."
            }
        ]
    elif "geography" in topic_lower:
        questions_pool = [
            {
                "question_text": "What is the largest continent by area?",
                "options": ["Africa", "Asia", "North America", "Europe"],
                "correct_answer_index": 1,
                "explanation": "Asia is the largest continent, covering about 30% of Earth's total land area."
            },
            {
                "question_text": "Which river is the longest in the world?",
                "options": ["Amazon", "Nile", "Mississippi", "Yangtze"],
                "correct_answer_index": 1,
                "explanation": "The Nile River is generally considered the longest river in the world at approximately 6,650 km."
            },
            {
                "question_text": "What is the deepest ocean trench?",
                "options": ["Puerto Rico Trench", "Japan Trench", "Mariana Trench", "Peru-Chile Trench"],
                "correct_answer_index": 2,
                "explanation": "The Mariana Trench in the Pacific Ocean is the deepest known part of Earth's oceans."
            },
            {
                "question_text": "Which country has the most time zones?",
                "options": ["Russia", "United States", "China", "Canada"],
                "correct_answer_index": 0,
                "explanation": "Russia spans 11 time zones, making it the country with the most time zones."
            },
            {
                "question_text": "What is the smallest country in the world?",
                "options": ["Monaco", "San Marino", "Vatican City", "Liechtenstein"],
                "correct_answer_index": 2,
                "explanation": "Vatican City is the smallest country in the world with an area of just 0.17 square miles."
            }
        ]
    elif "literature" in topic_lower or "english" in topic_lower:
        questions_pool = [
            {
                "question_text": "Who wrote the novel '1984'?",
                "options": ["Aldous Huxley", "George Orwell", "Ray Bradbury", "Isaac Asimov"],
                "correct_answer_index": 1,
                "explanation": "George Orwell wrote '1984', a dystopian novel published in 1949."
            },
            {
                "question_text": "What type of poem has 14 lines?",
                "options": ["Haiku", "Limerick", "Sonnet", "Ballad"],
                "correct_answer_index": 2,
                "explanation": "A sonnet is a poetic form with 14 lines, typically written in iambic pentameter."
            },
            {
                "question_text": "Who is the author of 'Pride and Prejudice'?",
                "options": ["Charlotte Brontë", "Emily Brontë", "Jane Austen", "George Eliot"],
                "correct_answer_index": 2,
                "explanation": "Jane Austen wrote 'Pride and Prejudice', published in 1813."
            },
            {
                "question_text": "What is the first book in the Harry Potter series?",
                "options": ["Chamber of Secrets", "Philosopher's Stone", "Prisoner of Azkaban", "Goblet of Fire"],
                "correct_answer_index": 1,
                "explanation": "The first Harry Potter book is 'Harry Potter and the Philosopher's Stone' (or Sorcerer's Stone in the US)."
            },
            {
                "question_text": "Which Shakespeare play features the characters Romeo and Juliet?",
                "options": ["Hamlet", "Macbeth", "Romeo and Juliet", "Othello"],
                "correct_answer_index": 2,
                "explanation": "Romeo and Juliet are the titular characters in Shakespeare's tragic play 'Romeo and Juliet'."
            }
        ]
    else:
        # Enhanced generic questions for other topics
        questions_pool = [
            {
                "question_text": f"What is considered a fundamental concept in {topic}?",
                "options": [f"Basic theory and principles", f"Advanced applications only", f"Historical context only", f"Modern trends only"],
                "correct_answer_index": 0,
                "explanation": f"Understanding basic theory and principles is fundamental to mastering {topic}."
            },
            {
                "question_text": f"Which approach is most effective when learning {topic}?",
                "options": [f"Memorization only", f"Practical application combined with theory", f"Theory only", f"Guessing"],
                "correct_answer_index": 1,
                "explanation": f"Combining practical application with theoretical understanding provides the best learning approach for {topic}."
            },
            {
                "question_text": f"What is a key benefit of studying {topic}?",
                "options": [f"Improved critical thinking", f"No practical value", f"Only academic interest", f"Temporary knowledge"],
                "correct_answer_index": 0,
                "explanation": f"Studying {topic} develops critical thinking skills and provides valuable knowledge for various applications."
            }
        ]
    
    # Select questions up to num_questions
    selected_questions = questions_pool[:min(num_questions, len(questions_pool))]
    
    # Add IDs and ensure we have enough questions
    for i, question in enumerate(selected_questions):
        question["id"] = f"q{i+1}"
        exam_data["questions"].append(question)
    
    # If we need more questions, repeat from the pool
    while len(exam_data["questions"]) < num_questions:
        remaining_needed = num_questions - len(exam_data["questions"])
        additional_questions = questions_pool[:remaining_needed]
        
        for j, question in enumerate(additional_questions):
            question_copy = question.copy()  # Create a copy to avoid modifying original
            question_copy["id"] = f"q{len(exam_data['questions'])+1}"
            exam_data["questions"].append(question_copy)
    
    return exam_data

def _create_structured_exam(topic: str, num_questions: int, difficulty: str, llm_response: str) -> dict:
    """Parse LLM response and create structured exam data."""
    # For now, fallback to knowledge-based until we implement LLM parsing
    return _create_knowledge_based_exam(topic, num_questions, difficulty)

class StudyMaterialInput(BaseModel):
    """Input schema for study material generation."""
    topic: str = Field(description="The topic to create study materials for")
    material_type: Optional[str] = Field(default="notes", description="Type of material: notes, flashcards, or summary")

@tool("generate_study_material", args_schema=StudyMaterialInput)
def generate_study_material(topic: str, material_type: str = "notes") -> str:
    """
    Generate study materials for a given topic.
    
    Creates educational content like notes, flashcards, or summaries
    to help students learn a subject.
    
    Args:
        topic: The subject to create study materials for
        material_type: Type of material - notes, flashcards, or summary
        
    Returns:
        Formatted study material content
    """
    try:
        logger.info(f"Generating {material_type} for topic: {topic}")
        
        if material_type == "flashcards":
            prompt = f"""Crea tarjetas de estudio (flashcards) sobre {topic}.

Genera 10 tarjetas de estudio en formato JSON:
{{
    "flashcards": [
        {{
            "id": 1,
            "front": "Pregunta o concepto",
            "back": "Respuesta o explicación"
        }}
    ]
}}

Asegúrate de cubrir los conceptos más importantes de {topic}."""

        elif material_type == "summary":
            prompt = f"""Crea un resumen completo y estructurado sobre {topic}.

El resumen debe incluir:
- Introducción al tema
- Conceptos principales 
- Puntos clave a recordar
- Conclusiones

Formato claro y educativo, ideal para estudiar."""

        else:  # default to notes
            prompt = f"""Crea apuntes detallados de estudio sobre {topic}.

Los apuntes deben incluir:
- Estructura clara con títulos y subtítulos
- Conceptos fundamentales explicados
- Ejemplos cuando sea apropiado
- Puntos importantes destacados
- Lista de términos clave

Formato educativo y fácil de estudiar."""

        return prompt
        
    except Exception as e:
        logger.error(f"Error in study material generation tool: {e}")
        return f"Error al generar material de estudio: {str(e)}"

# Tool registry for easy access
AVAILABLE_TOOLS = [
    generate_exam,
    generate_study_material
]

def get_exam_tools() -> list:
    """Get list of exam-related tools."""
    return [generate_exam, generate_study_material]

def get_all_tools() -> list:
    """Get all available tools."""
    return AVAILABLE_TOOLS 