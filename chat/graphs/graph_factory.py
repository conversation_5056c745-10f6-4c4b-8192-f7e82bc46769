"""
Graph Factory for managing LangGraph-based LLM providers.
Replicates the functionality of the existing LLM provider factory using LangGraph.
"""
import logging
from typing import Dict, Type, Optional, Any

from .base_llm_graph import BaseLLMGraph
from .grok_graph import GrokGraph
from .groq_graph import GroqGraph
from .gemini_graph import GeminiGraph
from .mistral_graph import MistralGraph
from .multi_agent_graph import MultiAgentGraph

logger = logging.getLogger(__name__)

class GraphLLMFactory:
    """
    Factory for creating and managing LangGraph-based LLM provider instances.
    Replicates the interface and functionality of the existing LLMProviderFactory.
    """
    
    # Registry of available graph providers (replicates original pattern)
    GRAPH_PROVIDERS: Dict[str, Type[BaseLLMGraph]] = {
        'grok': GrokGraph,
        'groq': GroqGraph,
        'gemini': GeminiGraph,
        'mistral': MistralGraph,
        'multi_agent': MultiAgentGraph,
    }
    
    # Provider instances cache (like original factory)
    _instances: Dict[str, BaseLLMGraph] = {}
    
    @classmethod
    def get_provider(cls, provider_name: str, model_name: Optional[str] = None) -> Optional[BaseLLMGraph]:
        """
        Get a LangGraph-based provider instance.
        
        Args:
            provider_name: Name of the provider ('grok', 'groq', 'gemini', etc.)
            model_name: Optional specific model name
            
        Returns:
            LangGraph provider instance or None if not found
        """
        try:
            provider_name = provider_name.lower()
            
            if provider_name not in cls.GRAPH_PROVIDERS:
                logger.error(f"Graph provider '{provider_name}' not found")
                return None
            
            # Create cache key including model name for uniqueness
            cache_key = f"{provider_name}_{model_name}" if model_name else provider_name
            
            # Return cached instance if exists and no specific model requested
            if cache_key in cls._instances and not model_name:
                logger.debug(f"Returning cached graph provider: {provider_name}")
                return cls._instances[cache_key]
            
            # Create new instance
            provider_class = cls.GRAPH_PROVIDERS[provider_name]
            instance = provider_class(model_name=model_name)
            
            # Cache the instance
            cls._instances[cache_key] = instance
            
            logger.info(f"Created new graph provider: {provider_name} with model: {instance.model_name}")
            return instance
            
        except Exception as e:
            logger.error(f"Error creating graph provider '{provider_name}': {e}")
            return None
    
    @classmethod
    def get_available_providers(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all available graph providers.
        
        Returns:
            Dictionary with provider information
        """
        providers_info = {}
        
        for name, provider_class in cls.GRAPH_PROVIDERS.items():
            try:
                # Get basic provider info
                providers_info[name] = {
                    'name': name,
                    'class': provider_class.__name__,
                    'available_models': provider_class.get_available_models(),
                    'description': f"LangGraph-based {name.capitalize()} provider",
                    'graph_based': True
                }
                
                # Add model count
                model_count = len(provider_class.AVAILABLE_MODELS)
                providers_info[name]['model_count'] = model_count
                
            except Exception as e:
                logger.error(f"Error getting info for provider '{name}': {e}")
                providers_info[name] = {
                    'name': name,
                    'error': str(e),
                    'available': False
                }
        
        return providers_info
    
    @classmethod
    def list_provider_names(cls) -> list:
        """Get list of available provider names."""
        return list(cls.GRAPH_PROVIDERS.keys())
    
    @classmethod
    def is_provider_available(cls, provider_name: str) -> bool:
        """Check if a provider is available."""
        return provider_name.lower() in cls.GRAPH_PROVIDERS
    
    @classmethod
    def get_provider_models(cls, provider_name: str) -> Dict[str, Dict[str, Any]]:
        """
        Get available models for a specific provider.
        
        Args:
            provider_name: Name of the provider
            
        Returns:
            Dictionary of available models
        """
        provider_name = provider_name.lower()
        
        if provider_name not in cls.GRAPH_PROVIDERS:
            logger.error(f"Provider '{provider_name}' not found")
            return {}
        
        provider_class = cls.GRAPH_PROVIDERS[provider_name]
        return provider_class.get_available_models()
    
    @classmethod
    def create_provider_with_config(cls, config: Dict[str, Any]) -> Optional[BaseLLMGraph]:
        """
        Create a provider instance with custom configuration.
        
        Args:
            config: Configuration dictionary with 'provider', 'model', etc.
            
        Returns:
            Provider instance or None
        """
        try:
            provider_name = config.get('provider')
            model_name = config.get('model')
            
            if not provider_name:
                logger.error("Provider name not specified in config")
                return None
            
            # Get the provider instance
            instance = cls.get_provider(provider_name, model_name)
            
            if not instance:
                return None
            
            # Apply additional configuration if provided
            if 'temperature' in config:
                instance.temperature = config['temperature']
            
            if 'max_tokens' in config:
                instance.max_tokens = config['max_tokens']
            
            logger.info(f"Created configured graph provider: {provider_name}")
            return instance
            
        except Exception as e:
            logger.error(f"Error creating configured graph provider: {e}")
            return None
    
    @classmethod
    def clear_cache(cls) -> None:
        """Clear the provider instances cache."""
        cls._instances.clear()
        logger.info("Graph provider cache cleared")
    
    @classmethod
    def get_cached_providers(cls) -> Dict[str, str]:
        """Get information about cached provider instances."""
        cached_info = {}
        for key, instance in cls._instances.items():
            cached_info[key] = {
                'provider': instance.provider_name,
                'model': instance.model_name,
                'class': instance.__class__.__name__
            }
        return cached_info
    
    @classmethod
    def validate_provider_config(cls, provider_name: str, model_name: Optional[str] = None) -> bool:
        """
        Validate if a provider and model configuration is valid.
        
        Args:
            provider_name: Name of the provider
            model_name: Optional model name
            
        Returns:
            True if configuration is valid
        """
        try:
            # Check if provider exists
            if not cls.is_provider_available(provider_name):
                return False
            
            # If model specified, check if it's available
            if model_name:
                available_models = cls.get_provider_models(provider_name)
                if model_name not in available_models:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating provider config: {e}")
            return False 