"""
Base LLM Graph class for LangGraph-based LLM providers.
This replicates the current LLM provider functionality using LangGraph.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, TypedDict, Union
from datetime import datetime

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

# Import tools
from .tools import get_all_tools

logger = logging.getLogger(__name__)

class LLMGraphState(TypedDict):
    """State structure for LLM graph execution."""
    messages: List[BaseMessage]
    user_input: str
    response: Optional[str]
    error: Optional[str]
    model_name: str
    provider_name: str
    memory_context: Optional[str]
    api_call_count: int
    metadata: Dict[str, Any]
    # Tool execution state
    tool_calls: Optional[List[Dict[str, Any]]]
    tool_results: Optional[List[Any]]
    structured_response: Optional[Dict[str, Any]]
    explanation_text: Optional[str]

class BaseLLMGraph(ABC):
    """
    Abstract base class for LangGraph-based LLM provider implementations.
    Replicates the interface and functionality of existing LLM providers.
    """
    
    # Must be defined by subclasses
    AVAILABLE_MODELS: Dict[str, Dict[str, Any]] = {}
    
    def __init__(self, model_name: Optional[str] = None):
        """Initialize the LLM graph."""
        self.provider_name = self.__class__.__name__.replace('Graph', '').lower()
        self._model_name = model_name or self._get_default_model()
        self.graph: Optional[StateGraph] = None
        self.compiled_graph = None
        self.memory_saver = MemorySaver()
        self.logger = logging.getLogger(f"{__name__}.{self.provider_name}")
        
        # Provider-specific settings (to be set by subclasses)
        self.api_key: Optional[str] = None
        self.api_base_url: Optional[str] = None
        self.temperature: float = 0.7
        self.max_tokens: int = 4096
        
        # Initialize provider settings (this will override the defaults above)
        self._initialize_provider_settings()
        
        # Initialize tools
        self.tools = get_all_tools()
        
        # Initialize the graph
        self._build_graph()
        self._compile_graph()
        
        self.logger.info(f"Initialized {self.provider_name} graph with model: {self._model_name}")
    
    @abstractmethod
    def _get_default_model(self) -> str:
        """Get the default model for this provider."""
        pass
    
    @abstractmethod
    def _initialize_provider_settings(self) -> None:
        """Initialize provider-specific settings (API keys, URLs, etc.)."""
        pass
    
    def _build_graph(self) -> None:
        """Build the LLM graph structure."""
        # Create the graph
        self.graph = StateGraph(LLMGraphState)
        
        # Add nodes for the LLM workflow
        self.graph.add_node("prepare_request", self._prepare_request)
        self.graph.add_node("validate_input", self._validate_input)
        self.graph.add_node("call_llm", self._call_llm)
        self.graph.add_node("process_response", self._process_response)
        self.graph.add_node("handle_error", self._handle_error)
        self.graph.add_node("update_memory", self._update_memory)
        
        # Define the workflow edges
        self.graph.add_edge(START, "prepare_request")
        self.graph.add_edge("prepare_request", "validate_input")
        
        # Conditional routing after validation
        self.graph.add_conditional_edges(
            "validate_input",
            self._route_after_validation,
            {
                "proceed": "call_llm",
                "error": "handle_error"
            }
        )
        
        # Conditional routing after LLM call
        self.graph.add_conditional_edges(
            "call_llm",
            self._route_after_llm_call,
            {
                "success": "process_response",
                "error": "handle_error"
            }
        )
        
        self.graph.add_edge("process_response", "update_memory")
        self.graph.add_edge("update_memory", END)
        self.graph.add_edge("handle_error", END)
    
    def _compile_graph(self) -> None:
        """Compile the graph for execution."""
        if self.graph is None:
            raise ValueError(f"Graph not built for {self.provider_name}")
        
        try:
            self.compiled_graph = self.graph.compile(
                checkpointer=self.memory_saver,
                interrupt_before=[],
                interrupt_after=[]
            )
            self.logger.info(f"Successfully compiled graph for {self.provider_name}")
        except Exception as e:
            self.logger.error(f"Failed to compile graph for {self.provider_name}: {e}")
            raise
    
    def _prepare_request(self, state: LLMGraphState) -> LLMGraphState:
        """Prepare the request for the LLM."""
        try:
            # Set provider and model info
            state["provider_name"] = self.provider_name
            state["model_name"] = self._model_name
            state["api_call_count"] = 0
            
            # Initialize metadata
            state["metadata"] = {
                "timestamp": datetime.now().isoformat(),
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }
            
            self.logger.debug(f"Request prepared for {self.provider_name} with model {self._model_name}")
            return state
            
        except Exception as e:
            self.logger.error(f"Error preparing request: {e}")
            state["error"] = f"Request preparation error: {str(e)}"
            return state
    
    def _validate_input(self, state: LLMGraphState) -> LLMGraphState:
        """Validate the input and configuration."""
        try:
            # Check if API key is configured
            if not self.api_key:
                state["error"] = f"API key not configured for {self.provider_name}"
                return state
            
            # Check if user input is provided
            if not state.get("user_input"):
                state["error"] = "No user input provided"
                return state
            
            # Check if model is available
            if self._model_name not in self.AVAILABLE_MODELS:
                self.logger.warning(f"Model {self._model_name} not in available models, using default")
                self._model_name = self._get_default_model()
                state["model_name"] = self._model_name
            
            self.logger.debug("Input validation passed")
            return state
            
        except Exception as e:
            self.logger.error(f"Error validating input: {e}")
            state["error"] = f"Input validation error: {str(e)}"
            return state
    
    def _route_after_validation(self, state: LLMGraphState) -> str:
        """Route after input validation."""
        return "error" if state.get("error") else "proceed"
    
    @abstractmethod
    def _call_llm(self, state: LLMGraphState) -> LLMGraphState:
        """Make the actual LLM API call. Must be implemented by subclasses."""
        pass
    
    def _route_after_llm_call(self, state: LLMGraphState) -> str:
        """Route after LLM call."""
        return "error" if state.get("error") else "success"
    
    def _process_response(self, state: LLMGraphState) -> LLMGraphState:
        """Process the LLM response."""
        try:
            response = state.get("response", "")
            if not response:
                state["error"] = "Empty response from LLM"
                return state
            
            # Update metadata
            state["metadata"]["response_length"] = len(response)
            state["metadata"]["processed_at"] = datetime.now().isoformat()
            
            self.logger.debug(f"Response processed: {len(response)} characters")
            return state
            
        except Exception as e:
            self.logger.error(f"Error processing response: {e}")
            state["error"] = f"Response processing error: {str(e)}"
            return state
    
    def _update_memory(self, state: LLMGraphState) -> LLMGraphState:
        """Update the conversation memory."""
        try:
            # Memory will be updated by the calling code
            # This is just a placeholder for consistency
            state["metadata"]["memory_updated"] = True
            self.logger.debug("Memory update prepared")
            return state
            
        except Exception as e:
            self.logger.error(f"Error updating memory: {e}")
            state["error"] = f"Memory update error: {str(e)}"
            return state
    
    def _handle_error(self, state: LLMGraphState) -> LLMGraphState:
        """Handle errors in the workflow."""
        error_msg = state.get("error", "Unknown error")
        
        # Provide appropriate error response based on error type
        if "api key" in error_msg.lower():
            response = f"Error de configuración: La clave API de {self.provider_name} no está configurada. Por favor, contacta al administrador del sistema."
        elif "rate limit" in error_msg.lower():
            response = f"Error: Se ha excedido el límite de solicitudes a la API de {self.provider_name}. Por favor, intenta de nuevo más tarde."
        elif "unauthorized" in error_msg.lower():
            response = f"Error: No autorizado. La clave API de {self.provider_name} no es válida o ha expirado."
        elif "connection" in error_msg.lower() or "timeout" in error_msg.lower():
            response = f"Error: No se pudo establecer conexión con el servicio de {self.provider_name}. Por favor, verifica tu conexión a Internet."
        else:
            response = f"Error: No se pudo completar la solicitud al modelo de lenguaje. Detalles: {error_msg}"
        
        state["response"] = response
        state["metadata"]["error_handled"] = True
        
        self.logger.warning(f"Error handled: {error_msg}")
        return state
    
    def query(self, user_input: str, memory_instance: ConversationBufferWindowMemory, request_type: str = None) -> str:
        """
        Execute the LLM graph workflow to process the query.
        This method runs the compiled LangGraph and returns the final response.
        """
        try:
            # Initialize state for the graph execution
            initial_state: LLMGraphState = {
                "messages": [],
                "user_input": user_input,
                "response": None,
                "error": None,
                "model_name": self._model_name,
                "provider_name": self.provider_name,
                "memory_context": None,
                "api_call_count": 0,
                "metadata": {},
                # Tool execution state
                "tool_calls": None,
                "tool_results": None,
                "structured_response": None,
                "explanation_text": None
            }
            
            # Extract memory context if available
            if memory_instance and hasattr(memory_instance, 'chat_memory'):
                try:
                    messages = memory_instance.chat_memory.messages
                    initial_state["messages"] = messages
                    initial_state["memory_context"] = str(memory_instance.buffer)
                except Exception as e:
                    self.logger.warning(f"Could not extract memory context: {e}")
            
            # Create a unique thread ID for this conversation
            thread_id = f"{self.provider_name}_{datetime.now().timestamp()}"
            config = {"configurable": {"thread_id": thread_id}}
            
            # Execute the graph workflow
            self.logger.info(f"Executing {self.provider_name} graph workflow for query")
            
            # Run the compiled graph
            final_state = self.compiled_graph.invoke(initial_state, config)
            
            # Check for errors in the final state
            if final_state.get("error"):
                error = final_state["error"]
                self.logger.error(f"Graph execution failed: {error}")
                
                # Return appropriate error responses based on error type
                if error == "unauthorized":
                    return "Error: API key no válida o no autorizada."
                elif error == "rate limit":
                    return "Error: Límite de velocidad alcanzado. Intenta de nuevo más tarde."
                elif error == "bad request":
                    return "Error: Solicitud inválida."
                elif error == "connection":
                    return "Error: Problema de conexión con el servicio."
                else:
                    return f"Error: {error}"
            
            # Extract the response
            response = final_state.get("response")
            if not response:
                self.logger.error("No response generated from graph execution")
                return "Error: No se pudo generar una respuesta."
            
            # Handle combined responses (explanation + structured data)
            if final_state.get("structured_response") and final_state.get("explanation_text"):
                # We have both an explanation and structured data (like an exam)
                combined_response = {
                    "response_type": "combined",
                    "explanation": final_state["explanation_text"],
                    "structured_data": final_state["structured_response"]
                }
                
                # Update memory with the explanation text
                if memory_instance:
                    try:
                        memory_instance.chat_memory.add_user_message(user_input)
                        memory_instance.chat_memory.add_ai_message(final_state["explanation_text"])
                    except Exception as e:
                        self.logger.warning(f"Could not update memory: {e}")
                
                self.logger.info(f"Graph execution completed successfully for {self.provider_name} with combined response")
                return combined_response
            
            # Handle different response types (text vs structured data like exams)
            if isinstance(response, dict):
                # For structured responses (exams, images, etc.), return the dict as-is
                if response.get("response_type") in ["interactive_test", "image"]:
                    # Update memory with a text representation
                    if memory_instance:
                        try:
                            memory_instance.chat_memory.add_user_message(user_input)
                            if response.get("response_type") == "interactive_test":
                                memory_instance.chat_memory.add_ai_message("He generado un examen interactivo basado en tu solicitud.")
                            else:
                                memory_instance.chat_memory.add_ai_message(str(response))
                        except Exception as e:
                            self.logger.warning(f"Could not update memory: {e}")
                    
                    self.logger.info(f"Graph execution completed successfully for {self.provider_name} with structured response")
                    return response  # Return the dict for structured responses
            
            # Update memory with the interaction for text responses
            if memory_instance:
                try:
                    memory_instance.chat_memory.add_user_message(user_input)
                    memory_instance.chat_memory.add_ai_message(str(response))
                except Exception as e:
                    self.logger.warning(f"Could not update memory: {e}")
            
            self.logger.info(f"Graph execution completed successfully for {self.provider_name}")
            return str(response)
            
        except Exception as e:
            self.logger.error(f"Error executing graph workflow: {e}")
            return f"Error: Falló la ejecución del grafo - {str(e)}"
    
    def set_model(self, model_name: str) -> None:
        """Set the model to use."""
        if model_name in self.AVAILABLE_MODELS:
            self._model_name = model_name
            # Update model-specific settings
            model_info = self.AVAILABLE_MODELS[model_name]
            self.max_tokens = model_info.get('max_tokens', 4096)
            self.logger.info(f"{self.provider_name} model changed to: {model_name}")
        else:
            self.logger.warning(f"{self.provider_name} model '{model_name}' not recognized. Maintaining {self._model_name}.")
    
    @property
    def model_name(self) -> str:
        """Get the current model name."""
        return self._model_name
    
    @property
    def current_model(self) -> str:
        """Get the current model name (alias for compatibility)."""
        return self._model_name
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model."""
        model_info = self.AVAILABLE_MODELS.get(self._model_name, {})
        return {
            "name": self._model_name,
            "provider": self.provider_name,
            "max_tokens": model_info.get('max_tokens', self.max_tokens),
            "temperature": self.temperature,
            "capabilities": model_info.get('capabilities', ['text']),
            "description": model_info.get('description', ''),
            "graph_based": True
        }
    
    @classmethod
    def get_available_models(cls) -> Dict[str, Dict[str, Any]]:
        """Get available models for this provider."""
        return cls.AVAILABLE_MODELS
    
    def format_exam_query(self, user_input: str) -> str:
        """Format exam queries (replicates existing functionality)."""
        from ..llm_providers.base import get_exam_prompt
        return get_exam_prompt(user_input) 