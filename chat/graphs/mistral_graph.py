"""
Mistral Graph implementation using LangGraph.
Replicates the functionality of the existing Mistral provider using LangGraph framework.
"""
import logging
from typing import Dict, Any, Optional
from django.conf import settings

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_mistralai import ChatMistralAI

from .base_llm_graph import BaseLLMGraph, LLMGraphState

logger = logging.getLogger(__name__)

class MistralGraph(BaseLLMGraph):
    """
    LangGraph implementation of the Mistral provider.
    Replicates all functionality from the original MistralProvider.
    """
    
    # Replicated from original MistralProvider
    AVAILABLE_MODELS = {
        'mistral-large-latest': {
            'id': 'mistral-large-latest',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 128000,
            'description': 'Modelo Mistral Large más reciente - máximo rendimiento'
        },
        'mistral-small-latest': {
            'id': 'mistral-small-latest',
            'capabilities': ['text', 'code'],
            'max_tokens': 32000,
            'description': 'Modelo Mistral Small más reciente - equilibrio costo-rendimiento'
        },
        'open-mistral-nemo': {
            'id': 'open-mistral-nemo',
            'capabilities': ['text', 'code'],
            'max_tokens': 128000,
            'description': 'Modelo Mistral Nemo open source'
        },
        'codestral-latest': {
            'id': 'codestral-latest',
            'capabilities': ['code', 'text'],
            'max_tokens': 32000,
            'description': 'Modelo especializado en programación'
        }
    }
    
    def __init__(self, model_name: Optional[str] = None):
        """Initialize the Mistral graph."""
        # Initialize provider settings before calling parent - REMOVED since base class now handles it
        super().__init__(model_name)
        
        # Initialize Mistral chat instance (like original)
        self._mistral_chat = None
        self._create_mistral_chat()
    
    def _get_default_model(self) -> str:
        """Get the default Mistral model."""
        return "mistral-large-latest"
    
    def _initialize_provider_settings(self) -> None:
        """Initialize Mistral-specific settings."""
        self.api_key = settings.MISTRAL_API_KEY
        self.temperature = 0.7
        
        if not self.api_key:
            logger.warning("MISTRAL_API_KEY no está configurada. Asegúrate de configurar esta variable de entorno.")
        else:
            logger.info(f"Usando clave API de Mistral: {self.api_key[:10]}...")
    
    def _create_mistral_chat(self) -> None:
        """Create ChatMistralAI instance (replicates original approach)."""
        try:
            self._mistral_chat = ChatMistralAI(
                model=self._model_name,
                mistral_api_key=self.api_key,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            logger.debug(f"ChatMistralAI instance created for model: {self._model_name}")
        except Exception as e:
            logger.error(f"Error creating ChatMistralAI instance: {e}")
            self._mistral_chat = None
    
    def set_model(self, model_name: str) -> None:
        """Set the model and recreate Mistral instance (replicates original)."""
        super().set_model(model_name)
        if model_name in self.AVAILABLE_MODELS:
            # Recreate the Mistral client with the new model (same as original)
            self._create_mistral_chat()
    
    def _call_llm(self, state: LLMGraphState) -> LLMGraphState:
        """Make the Mistral API call using LangGraph state."""
        try:
            state["api_call_count"] += 1
            
            # Explicit API key validation
            if not self.api_key:
                self.logger.error("API key is None or empty at call time")
                state["error"] = "API key not configured for mistral"
                return state
                
            # Log API key status for debugging
            self.logger.info(f"API key available for Mistral: {bool(self.api_key)}")
            if self.api_key:
                self.logger.info(f"API key starts with: {self.api_key[:10]}...")
            
            # Prepare messages for ChatMistralAI
            messages = []
            
            # Add conversation history from memory if available
            if state.get("messages"):
                for msg in state["messages"]:
                    if hasattr(msg, 'type'):
                        if msg.type == "human":
                            messages.append(HumanMessage(content=msg.content))
                        elif msg.type == "ai":
                            messages.append(AIMessage(content=msg.content))
                        elif msg.type == "system":
                            messages.append(SystemMessage(content=msg.content))

            # Add the current user input
            user_input = state["user_input"]
            
            # Add a subtle system context about available tools (only when tools are present)
            if self.tools:
                system_msg = SystemMessage(content="""You are an educational assistant with access to specialized tools for creating interactive educational content.

Available tools:
- generate_exam: Creates interactive quizzes/tests with multiple choice questions
- generate_study_material: Creates study guides and educational materials

When users ask for quizzes, tests, exams, assessments, or want to check their knowledge on a topic, use the generate_exam tool to create structured educational content. The tool handles the complexity of question generation and formatting.

For other educational requests like study guides or notes, use generate_study_material.

Always use tools when they can provide better structured educational experiences than plain text responses.""")
                messages.insert(0, system_msg)
            
            # Let the LLM naturally decide when to use tools - no forced guidance
            messages.append(HumanMessage(content=user_input))

            # Initialize ChatMistralAI with the correct parameter name
            llm = ChatMistralAI(
                mistral_api_key=self.api_key,  # Use mistral_api_key parameter name
                model=self.model_name,
                temperature=self.temperature
            )
            
            # Bind tools to the LLM if available
            if self.tools:
                try:
                    llm_with_tools = llm.bind_tools(self.tools)
                    self.logger.info(f"Bound {len(self.tools)} tools to Mistral LLM")
                    
                    # Invoke LLM with tools bound
                    self.logger.info(f"Calling Mistral LLM with {len(messages)} messages")
                    try:
                        response = llm_with_tools.invoke(messages)
                        self.logger.info(f"LLM response type: {type(response)}")
                        
                        # Check if response contains tool calls
                        if hasattr(response, 'tool_calls') and response.tool_calls:
                            self.logger.info(f"Tool calls detected: {len(response.tool_calls)}")
                            state["tool_calls"] = response.tool_calls
                            
                            # Execute tools and collect results
                            tool_results = []
                            for tool_call in response.tool_calls:
                                self.logger.info(f"Executing tool: {tool_call['name']} with args: {tool_call['args']}")
                                try:
                                    tool_result = self._execute_tool_call(tool_call)
                                    tool_results.append(tool_result)
                                    self.logger.info(f"Tool {tool_call['name']} executed successfully")
                                except Exception as e:
                                    self.logger.error(f"Tool execution failed: {e}")
                                    tool_results.append({"error": str(e)})
                            
                            state["tool_results"] = tool_results
                            return self._handle_tool_calls(state)
                        else:
                            self.logger.info("No tool calls detected in response")
                            if hasattr(response, 'tool_calls'):
                                self.logger.info(f"Tool calls attribute exists but empty: {response.tool_calls}")
                            else:
                                self.logger.info("No tool_calls attribute in response")
                        
                        # Regular text response
                        state["response"] = response.content
                        state["api_call_count"] = 1
                        
                        self.logger.info(f"Mistral response generated: {len(response.content)} characters")
                        return state
                    except Exception as e:
                        self.logger.error(f"Error calling Mistral LLM: {e}")
                        state["error"] = str(e)
                        return state
                except Exception as e:
                    self.logger.warning(f"Error with tools, falling back to regular call: {e}")
                    response = llm.invoke(messages)
                    response_content = response.content if hasattr(response, 'content') else str(response)
                    state["metadata"]["tools_used"] = False
            else:
                # Make regular API call without tools
                response = llm.invoke(messages)
                response_content = response.content if hasattr(response, 'content') else str(response)
                state["metadata"]["tools_used"] = False
            
            if not response_content:
                state["error"] = "Empty response from Mistral API"
                return state
            
            state["response"] = response_content
            state["metadata"]["api_call_successful"] = True
            state["metadata"]["model_used"] = self._model_name
            
            self.logger.info(f"Mistral API call successful with model {self._model_name}")
            return state
            
        except Exception as e:
            self.logger.error(f"Error calling Mistral API - Original error: {type(e).__name__}: {str(e)}")
            error_message = str(e).lower()
            
            # Handle different error types
            if "unauthorized" in error_message or "invalid api key" in error_message:
                state["error"] = "unauthorized"
            elif "rate limit" in error_message or "too many requests" in error_message:
                state["error"] = "rate limit"
            elif "bad request" in error_message or "invalid request" in error_message:
                state["error"] = "bad request"
            elif "connection" in error_message or "timeout" in error_message:
                state["error"] = "connection"
            else:
                state["error"] = f"api_error: {str(e)}"
            
            return state
    
    def _handle_tool_calls(self, state: LLMGraphState) -> LLMGraphState:
        """Handle tool calls from LangChain response and update graph state."""
        try:
            self.logger.info(f"Processing {len(state['tool_calls'])} tool calls")
            
            # Store tool calls in state
            state["tool_calls"] = state["tool_calls"]
            
            # Execute tool calls
            tool_results = []
            for tool_call in state["tool_calls"]:
                tool_name = tool_call.get('name') or tool_call.get('function', {}).get('name')
                tool_args = tool_call.get('args') or tool_call.get('arguments', {})
                
                self.logger.info(f"Executing tool: {tool_name} with args: {tool_args}")
                
                # Find and execute the tool
                tool_result = None
                for tool in self.tools:
                    if tool.name == tool_name:
                        try:
                            tool_result = tool.invoke(tool_args)
                            break
                        except Exception as e:
                            tool_result = f"Error executing tool {tool_name}: {str(e)}"
                            self.logger.error(f"Tool execution error: {e}")
                
                if tool_result is None:
                    tool_result = f"Tool {tool_name} not found"
                
                tool_results.append(tool_result)
            
            # Store tool results in state
            state["tool_results"] = tool_results
            
            # Check if this is an exam generation response
            for i, tool_call in enumerate(state["tool_calls"]):
                if tool_call.get('name') == 'generate_exam' and i < len(tool_results):
                    tool_result = tool_results[i]
                    
                    # The tool now returns the complete exam JSON directly
                    if isinstance(tool_result, dict) and 'title' in tool_result and 'questions' in tool_result:
                        self.logger.info(f"Tool generated exam: {tool_result['title']} with {len(tool_result['questions'])} questions")
                        
                        # Store structured response in state for UI
                        state["structured_response"] = {
                            "response_type": "interactive_test",
                            "data": tool_result
                        }
                        
                        # Create a friendly explanation
                        topic = tool_call.get('args', {}).get('topic', 'el tema solicitado')
                        num_questions = len(tool_result['questions'])
                        explanation_text = f"¡He creado un examen interactivo sobre {topic} con {num_questions} preguntas! Puedes tomar el examen usando la interfaz interactiva a continuación. ¡Buena suerte!"
                        
                        # Store explanation in state
                        state["explanation_text"] = explanation_text
                        
                        # CRITICAL: Set the response in state so base graph doesn't think it's empty
                        state["response"] = explanation_text
                        
                        self.logger.info(f"Created combined response with exam and explanation")
                        
                        # Return the explanation as the primary response
                        return state
                    else:
                        self.logger.warning(f"Tool result is not valid exam JSON: {type(tool_result)}")
            
            # For non-exam tools, create a regular response
            tool_summary = f"Executed {len(state['tool_calls'])} tools successfully."
            state["response"] = tool_summary
            return state
            
        except Exception as e:
            self.logger.error(f"Error in _handle_tool_calls: {e}")
            return f"Error processing tool calls: {str(e)}"
    
    def _execute_tool_call(self, tool_call):
        """Execute a single tool call and return the result."""
        tool_name = tool_call.get('name')
        tool_args = tool_call.get('args', {})
        
        # Find the tool by name
        for tool in self.tools:
            if tool.name == tool_name:
                return tool.invoke(tool_args)
        
        raise ValueError(f"Tool {tool_name} not found")
    
    def _handle_error(self, state: LLMGraphState) -> LLMGraphState:
        """Handle Mistral-specific errors (replicates original error handling)."""
        error_msg = state.get("error", "Unknown error")
        
        # Handle specific error types from original implementation
        if error_msg == "rate_limit":
            response = "Error: Se ha excedido el límite de solicitudes a la API de Mistral. Por favor, intenta de nuevo más tarde."
        elif error_msg == "unauthorized":
            response = "Error: No autorizado. La clave API de Mistral no es válida o ha expirado."
        elif error_msg == "quota_exceeded":
            response = "Error: Se ha excedido la cuota de la API de Mistral. Por favor, verifica tu plan de facturación."
        elif error_msg.startswith("mistral_api_error"):
            response = f"Error: {error_msg.replace('mistral_api_error: ', '')}"
        elif "api key" in error_msg.lower():
            response = "Error de configuración: La clave API de Mistral no está configurada. Por favor, contacta al administrador del sistema."
        else:
            # Fallback to base implementation
            return super()._handle_error(state)
        
        state["response"] = response
        state["metadata"]["error_handled"] = True
        state["metadata"]["error_type"] = error_msg
        
        logger.warning(f"Mistral error handled: {error_msg}")
        return state 