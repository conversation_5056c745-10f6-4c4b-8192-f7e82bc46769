"""
Grok Graph implementation using LangGraph.
Replicates the functionality of the existing Grok provider using LangGraph framework.
"""
import logging
import base64
import json
from typing import Dict, Any, Optional, Union
from django.conf import settings

from langchain_core.messages import HumanMessage, AIMessage
from openai import OpenAI

from .base_llm_graph import BaseLLMGraph, LLMGraphState

logger = logging.getLogger(__name__)

class GrokGraph(BaseLLMGraph):
    """
    LangGraph implementation of the Grok provider.
    Replicates all functionality from the original GrokProvider.
    """
    
    # Replicated from original GrokProvider
    AVAILABLE_MODELS = {
        'grok-3-beta': {
            'id': 'grok-3-beta',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Modelo principal de Grok 3, ideal para tareas complejas'
        },
        'grok-3-fast': {
            'id': 'grok-3-fast',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión rápida de Grok 3 para aplicaciones sensibles a la latencia'
        },
        'grok-3-mini': {
            'id': 'grok-3-mini',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión ligera de Grok 3, optimizada para tareas lógicas'
        },
        'grok-3-mini-fast': {
            'id': 'grok-3-mini-fast',
            'capabilities': ['text', 'code', 'reasoning'],
            'max_tokens': 131072,
            'description': 'Versión rápida y ligera de Grok 3'
        },
        'grok-2-vision-1212': {
            'id': 'grok-2-vision-1212',
            'capabilities': ['text', 'vision'],
            'max_tokens': 8192,
            'description': 'Modelo multimodal para procesar texto e imágenes'
        },
        'grok-2-image-1212': {
            'id': 'grok-2-image-1212',
            'capabilities': ['text', 'image_generation'],
            'max_tokens': 131072,
            'description': 'Modelo para generar imágenes a partir de texto'
        }
    }
    
    def __init__(self, model_name: Optional[str] = None):
        """Initialize the Grok graph."""
        # Initialize provider settings before calling parent
        self._initialize_provider_settings()
        super().__init__(model_name)
    
    def _get_default_model(self) -> str:
        """Get the default Grok model."""
        return "grok-3-beta"
    
    def _initialize_provider_settings(self) -> None:
        """Initialize Grok-specific settings."""
        # Try GROK_API_KEY first, then XAI_API_KEY as fallback (same as original)
        self.api_key = settings.GROK_API_KEY or settings.XAI_API_KEY
        self.api_base_url = "https://api.x.ai/v1"
        self.temperature = 0.7
        
        if not self.api_key:
            logger.warning("Ni GROK_API_KEY ni XAI_API_KEY están configuradas. Asegúrate de configurar al menos una de estas variables de entorno.")
        else:
            logger.info(f"Usando clave API de Grok/XAI: {self.api_key[:10]}...")
    
    def _call_llm(self, state: LLMGraphState) -> LLMGraphState:
        """Make the Grok API call."""
        try:
            state["api_call_count"] += 1
            
            # Create OpenAI client for Grok
            client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base_url
            )
            
            # Handle image generation models
            if self._model_name == "grok-2-image-1212":
                return self._handle_image_generation(state, client)
            
            # Prepare messages for the API call
            messages = []
            
            # Add conversation history from memory if available
            if state.get("messages"):
                for msg in state["messages"]:
                    if hasattr(msg, 'type'):
                        if msg.type == 'human':
                            messages.append({"role": "user", "content": msg.content})
                        elif msg.type == 'ai':
                            messages.append({"role": "assistant", "content": msg.content})
                    elif isinstance(msg, HumanMessage):
                        messages.append({"role": "user", "content": msg.content})
                    elif isinstance(msg, AIMessage):
                        messages.append({"role": "assistant", "content": msg.content})
            
            # Add the current user input
            messages.append({"role": "user", "content": state["user_input"]})
            
            self.logger.info(f"Making Grok API call with {len(messages)} messages")
            
            # Prepare tools for function calling if supported by the model
            tools_for_api = None
            if self._model_name in ["grok-3-beta", "grok-3-fast"]:  # Only newer models support tools
                try:
                    tools_for_api = []
                    for tool in self.tools:
                        tool_schema = {
                            "type": "function",
                            "function": {
                                "name": tool.name,
                                "description": tool.description,
                                "parameters": tool.args
                            }
                        }
                        tools_for_api.append(tool_schema)
                    self.logger.info(f"Prepared {len(tools_for_api)} tools for API call")
                except Exception as e:
                    self.logger.warning(f"Error preparing tools: {e}")
                    tools_for_api = None
            
            # Make the API call
            api_kwargs = {
                "model": self._model_name,
                "messages": messages,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }
            
            if tools_for_api:
                api_kwargs["tools"] = tools_for_api
                api_kwargs["tool_choice"] = "auto"
            
            completion = client.chat.completions.create(**api_kwargs)
            
            # Extract response
            response_message = completion.choices[0].message
            
            # Handle tool calls if present
            if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
                self.logger.info(f"Processing {len(response_message.tool_calls)} tool calls")
                response = self._handle_tool_calls(response_message.tool_calls, client, messages)
            else:
                response = response_message.content
            
            if not response:
                state["error"] = "Empty response from Grok API"
                return state
            
            state["response"] = response
            state["metadata"]["api_call_successful"] = True
            state["metadata"]["finish_reason"] = getattr(completion.choices[0], 'finish_reason', 'unknown')
            state["metadata"]["model_used"] = self._model_name
            state["metadata"]["tools_used"] = tools_for_api is not None
            
            self.logger.info(f"Grok API call successful with model {self._model_name}")
            return state
            
        except Exception as e:
            logger.error(f"Error calling Grok API: {e}")
            error_message = str(e).lower()
            
            # Replicate original error handling
            if "unauthorized" in error_message or "authentication" in error_message:
                state["error"] = "unauthorized"
            elif "rate limit" in error_message or "too many requests" in error_message:
                state["error"] = "rate limit"
            elif "bad request" in error_message or "invalid request" in error_message:
                state["error"] = "bad request"
            elif "connection" in error_message or "timeout" in error_message:
                state["error"] = "connection"
            else:
                state["error"] = f"api_error: {str(e)}"
            
            return state
    
    def _handle_image_generation(self, state: LLMGraphState, client: OpenAI) -> LLMGraphState:
        """Handle image generation with Grok."""
        try:
            logger.info(f"Using image generation model: {self._model_name}")
            
            # Call the image generation API
            response = client.images.generate(
                model=self._model_name,
                prompt=state["user_input"],
                n=1,
                response_format="url"
            )
            
            # Extract the image URL
            image_url = response.data[0].url
            
            # Return as dictionary format (same as original)
            state["response"] = {
                "response_type": "image",
                "data": image_url
            }
            state["metadata"]["image_generated"] = True
            state["metadata"]["image_url"] = image_url
            
            logger.info("Image generation successful")
            return state
            
        except Exception as img_error:
            logger.error(f"Error generating image with Grok: {img_error}")
            error_message = str(img_error).lower()
            
            if "endpoint" in error_message or "not found" in error_message:
                state["error"] = f"endpoint_error: {str(img_error)}"
            else:
                state["error"] = f"image_generation_error: {str(img_error)}"
            
            return state
    
    def _handle_tool_calls(self, tool_calls, client, messages):
        """Handle tool calls from the LLM response."""
        try:
            tool_messages = []
            
            for tool_call in tool_calls:
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)
                
                self.logger.info(f"Executing tool: {tool_name} with args: {tool_args}")
                
                # Find and execute the tool
                tool_result = None
                for tool in self.tools:
                    if tool.name == tool_name:
                        try:
                            tool_result = tool.invoke(tool_args)
                            break
                        except Exception as e:
                            tool_result = f"Error executing tool {tool_name}: {str(e)}"
                            self.logger.error(f"Tool execution error: {e}")
                
                if tool_result is None:
                    tool_result = f"Tool {tool_name} not found"
                
                # Add tool result to messages
                tool_messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": str(tool_result)
                })
            
            # Make another API call with tool results
            final_messages = messages + [
                {"role": "assistant", "tool_calls": [
                    {
                        "id": tc.id,
                        "type": "function",
                        "function": {"name": tc.function.name, "arguments": tc.function.arguments}
                    } for tc in tool_calls
                ]}
            ] + tool_messages
            
            final_completion = client.chat.completions.create(
                model=self._model_name,
                messages=final_messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            return final_completion.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error handling tool calls: {e}")
            return f"Error processing tools: {str(e)}"
    
    def _convert_messages_to_openai_format(self, messages):
        """Convert LangChain messages to OpenAI format."""
        openai_messages = []
        
        for message in messages:
            if hasattr(message, 'type'):
                if message.type == 'human':
                    openai_messages.append({"role": "user", "content": message.content})
                elif message.type == 'ai':
                    openai_messages.append({"role": "assistant", "content": message.content})
            elif hasattr(message, 'content'):
                if isinstance(message, HumanMessage):
                    openai_messages.append({"role": "user", "content": message.content})
                elif isinstance(message, AIMessage):
                    openai_messages.append({"role": "assistant", "content": message.content})
        
        return openai_messages
    
    def query_with_image(self, user_input: str, image_path: str, memory_instance) -> str:
        """
        Handle image queries (replicates original functionality).
        This method maintains the same interface as the original provider.
        """
        try:
            # Check if vision model is being used
            if 'vision' not in self.AVAILABLE_MODELS.get(self._model_name, {}).get('capabilities', []):
                logger.error(f"El modelo {self._model_name} no soporta procesamiento de imágenes.")
                return f"Error: El modelo {self._model_name} no soporta procesamiento de imágenes. Usa grok-2-vision-1212 u otro modelo con capacidad de visión."
            
            # Create OpenAI client
            client = OpenAI(
                api_key=self.api_key,
                base_url=self.api_base_url
            )
            
            # Read and encode image
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
            # Convert memory messages to OpenAI format
            messages = []
            if memory_instance and hasattr(memory_instance, 'chat_memory'):
                messages = self._convert_messages_to_openai_format(memory_instance.chat_memory.messages)
            
            # Create multimodal content
            content = [
                {"type": "text", "text": user_input},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                }
            ]
            
            # Add current message with image
            messages.append({"role": "user", "content": content})
            
            # Make API call
            completion = client.chat.completions.create(
                model=self._model_name,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )
            
            response = completion.choices[0].message.content
            
            # Update memory
            if memory_instance:
                memory_instance.chat_memory.add_user_message(f"{user_input} [Imagen adjunta]")
                memory_instance.chat_memory.add_ai_message(response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in Grok image query: {e}")
            return f"Error al procesar imagen con Grok: {str(e)}"
    
    def _handle_error(self, state: LLMGraphState) -> LLMGraphState:
        """Handle Grok-specific errors (replicates original error handling)."""
        error_msg = state.get("error", "Unknown error")
        
        # Handle specific error types from original implementation
        if error_msg == "unauthorized":
            response = "Error: No autorizado. La clave API de Grok no es válida o ha expirado."
        elif error_msg == "rate limit":
            response = "Error: Se ha excedido el límite de solicitudes a la API. Por favor, intenta de nuevo más tarde."
        elif error_msg == "bad request":
            response = "Error: La solicitud al modelo de lenguaje no es válida. Esto puede deberse a un problema con el formato de la solicitud."
        elif error_msg == "connection":
            response = "Error: No se pudo establecer conexión con el servicio de Grok. Por favor, verifica tu conexión a Internet."
        elif error_msg.startswith("endpoint_error"):
            response = f"Error: El modelo de generación de imágenes debe usarse con el endpoint correcto. Detalles: {error_msg.replace('endpoint_error: ', '')}"
        elif error_msg.startswith("image_generation_error"):
            response = f"Error al generar imagen: {error_msg.replace('image_generation_error: ', '')}"
        elif error_msg.startswith("api_error"):
            response = f"Error: No se pudo completar la solicitud al modelo de lenguaje. Detalles: {error_msg.replace('api_error: ', '')}"
        else:
            # Fallback to base implementation
            return super()._handle_error(state)
        
        state["response"] = response
        state["metadata"]["error_handled"] = True
        state["metadata"]["error_type"] = error_msg
        
        logger.warning(f"Grok error handled: {error_msg}")
        return state 