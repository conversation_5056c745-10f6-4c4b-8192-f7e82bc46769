/**
 * Módulo core.js - Funcionalidad principal del chat
 *
 * Este módulo contiene la lógica principal del chat, incluyendo:
 * - Inicialización del chat
 * - Envío y recepción de mensajes
 * - Gestión de chats
 * - Formateo de mensajes
 */

// Importar utilidades
import { escapeHtml, getCookie } from './utils.js';
import { setupCodeHighlighting, escapeHtmlInCodeBlocks } from './code-handler.js';
import { initializeEffects } from './effects.js';
import formatter from './formatter.js';
import { ExamHandler } from './exam-handler.js';
import { testFeature } from './test-feature.js';
import { fileUploadHandler } from './file-upload.js';


// Objeto principal para encapsular la lógica del chat
const ChatBot = {
  csrftoken: getCookie('csrftoken'),
  isFirstMessage: true,
  currentChatId: null,
  examHandler: new ExamHandler(),

  /**
   * Inicializa los event listeners y el estado del chat al cargar la página.
   */
  initialize() {
    document.addEventListener('DOMContentLoaded', () => {
      // Inicializar efectos visuales
      initializeEffects();

      // Cargar chat activo o mostrar estado inicial
      const activeChatId = localStorage.getItem('activeChatId');
      if (performance.getEntriesByType('navigation')[0].type === 'reload' && activeChatId) {
        this.loadChat(activeChatId);
      } else {
        this.displayInitialState();
        localStorage.removeItem('activeChatId');
      }

      // Configurar botón de nuevo chat
      const newChatButton = document.querySelector('#new-chat-btn');
      if (newChatButton) {
        newChatButton.addEventListener('click', () => this.newChat());
      } else {
        console.error('New chat button not found');
      }

      // Configurar formulario de chat
      const userInput = document.getElementById('user-input');
      const chatForm = document.querySelector('.chat-form');

      if (userInput && chatForm) {
        // Auto-resize del textarea y detectar intención de examen
        userInput.addEventListener('input', () => {
          // Auto-resize
          userInput.style.height = 'auto';
          userInput.style.height = (userInput.scrollHeight) + 'px';

          // Detectar intención de examen
          const text = userInput.value.trim();
          const isExamIntent = this.examHandler.detectExamIntent(text);

          // Mostrar sugerencia si se detecta intención de examen y no está ya activa o cancelada
          if (isExamIntent && !this.examHandler.isTestSuggestionActive && !this.examHandler.suggestionCancelled) {
            this.examHandler.isTestSuggestionActive = true;

            // Crear y mostrar banner de sugerencia
            const suggestionBanner = this.examHandler.createSuggestionBanner();
            const inputContainer = document.querySelector('.input-container');
            if (inputContainer && !document.querySelector('.exam-suggestion-banner')) {
              inputContainer.parentNode.insertBefore(suggestionBanner, inputContainer);
            }
          } else if (!isExamIntent && this.examHandler.isTestSuggestionActive) {
            // Eliminar sugerencia si ya no hay intención de examen
            this.examHandler.resetSuggestion();
          }
        });

        // Enviar mensaje con Enter (pero permitir nueva línea con Shift+Enter)
        userInput.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.sendMessage();
          }
        });

        // Enviar mensaje al hacer submit del formulario
        chatForm.addEventListener('submit', (e) => {
          e.preventDefault();
          this.sendMessage();
        });
      }

      // Configurar menú de perfil
      const profileIcon = document.getElementById('profile-icon');
      const profileMenu = document.getElementById('profile-menu');

      if (profileIcon && profileMenu) {
        profileIcon.addEventListener('click', () => {
          profileMenu.classList.toggle('active');
        });

        // Cerrar menú al hacer clic fuera
        document.addEventListener('click', (e) => {
          if (!profileIcon.contains(e.target) && !profileMenu.contains(e.target)) {
            profileMenu.classList.remove('active');
          }
        });
      }
    });
  },

  /**
   * Muestra el estado inicial del chat.
   */
  displayInitialState() {
    const chatBoxContent = document.getElementById('chat-box-content');
    if (!chatBoxContent) return;

    chatBoxContent.innerHTML = '';
    this.isFirstMessage = true;
    this.currentChatId = null;

    // Centrar el formulario de entrada verticalmente
    const chatForm = document.querySelector('.chat-form');
    if (chatForm) {
      chatForm.classList.add('centered-vertically');
    }

    // Input legend removed
  },

  /**
   * Crea un nuevo chat.
   */
  newChat() {
    this.displayInitialState();
    localStorage.removeItem('activeChatId');
  },

  /**
   * Envía un mensaje al servidor y muestra la respuesta.
   */
  async sendMessage() {
    const inputField = document.getElementById('user-input');
    const chatBoxContent = document.getElementById('chat-box-content');
    const message = inputField.value.trim();

    if (!message && !fileUploadHandler.hasFiles()) return;

    // Verificar si hay una sugerencia de examen activa o si el modo test está activo
    const isExamRequest = (this.examHandler.isTestSuggestionActive && !this.examHandler.suggestionCancelled) || testFeature.isTestModeActive();

    // Crear mensaje del usuario (incluir indicación de imagen si hay archivos)
    const displayMessage = fileUploadHandler.hasFiles() ? `${message} [Imagen adjunta]` : message;
    this.createUserMessage(displayMessage, chatBoxContent);
    // El scroll se maneja dentro de createUserMessage
    this.toggleInputBox();

    if (this.isFirstMessage) {
      this.handleFirstMessage();
    }

    // Actualizar UI para el chat
    this.handleChatUpdates();

    // Mostrar indicador de escritura
    const typingIndicator = this.createTypingIndicator();
    chatBoxContent.appendChild(typingIndicator);
    // Forzar scroll para mostrar indicador
    setTimeout(() => {
      this.forceScrollToElement(typingIndicator);
    }, 10);

    // Limpiar y deshabilitar input mientras se procesa
    inputField.value = '';
    inputField.style.height = 'auto';
    inputField.disabled = true;



    // Limpiar estado de sugerencia de examen y modo test
    this.examHandler.resetSuggestion();
    testFeature.reset();

    try {
      let data;

      {
        // Flujo normal para mensajes regulares
        // Verificar si hay archivos adjuntos
        const hasAttachment = fileUploadHandler.hasFiles();
        let response;

        if (hasAttachment) {
          // Enviar mensaje con archivo adjunto usando FormData
          const formData = new FormData();
          formData.append('message', message);
          formData.append('chat_id', this.currentChatId || '');
          formData.append('request_type', 'multimodal_query');

          // Agregar archivo
          const file = fileUploadHandler.getFiles()[0];
          formData.append('file', file);

          // Enviar al servidor
          response = await fetch('/chat/api/multimodal/', {
            method: 'POST',
            headers: {
              'X-CSRFToken': this.csrftoken,
            },
            body: formData,
          });

          // Limpiar archivos después de enviar
          fileUploadHandler.clearFiles();
        } else {
          // Preparar datos para enviar al servidor (mensaje de texto normal)
          const requestData = {
            message,
            chat_id: this.currentChatId,
            graph_mode: window.modelSelector ? window.modelSelector.isGraphMode : false
          };

          // Agregar tipo de solicitud si es un examen
          if (isExamRequest) {
            requestData.request_type = 'generate_test';
          }

          console.log('📤 Sending request with graph mode:', requestData.graph_mode);
          console.log('📋 Request data:', requestData);

          // Enviar mensaje al servidor
          response = await fetch('/chat/api/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRFToken': this.csrftoken,
            },
            body: JSON.stringify(requestData),
          });
        }

        console.log('📡 Response status:', response.status);
        console.log('📡 Response OK:', response.ok);
        
        if (!response.ok) throw new Error('Error en la respuesta del servidor');
        
        console.log('📥 Parsing JSON response...');
        data = await response.json();
        console.log('✅ JSON parsed successfully:', data);
      }

      // Eliminar indicador de escritura
      if (typingIndicator && typingIndicator.parentNode) {
        typingIndicator.parentNode.removeChild(typingIndicator);
      }

      // Debug: verificar que llegamos hasta aquí
      console.log('🎯 LLEGANDO A PROCESAR RESPUESTA');
      console.log('🔍 Data recibida antes de processResponse:', data);
      
      // Procesar respuesta
      try {
        this.processResponse(data, message, inputField);
      } catch (responseError) {
        console.error('❌ Error procesando respuesta:', responseError);
        console.error('📚 Stack trace:', responseError.stack);
        console.error('📦 Data recibida:', data);
        
        // Eliminar indicador de escritura
        if (typingIndicator && typingIndicator.parentNode) {
          typingIndicator.parentNode.removeChild(typingIndicator);
        }
        
        // Mostrar error específico al usuario
        this.createBotErrorMessage('Error procesando la respuesta. Intenta de nuevo.', chatBoxContent);
        
        // Habilitar input
        inputField.disabled = false;
        return;
      }
    } catch (error) {
      console.error('Error enviando mensaje:', error);
      console.error('Stack trace:', error.stack);

      // Eliminar indicador de escritura
      if (typingIndicator && typingIndicator.parentNode) {
        typingIndicator.parentNode.removeChild(typingIndicator);
      }

      // Mostrar error al usuario
      this.createBotErrorMessage('Hubo un error al procesar tu mensaje. Por favor, intenta de nuevo.', chatBoxContent);
    } finally {
      // Habilitar input y restaurar UI
      inputField.disabled = false;


    }
  },

  /**
   * Crea un indicador de escritura.
   * @returns {HTMLElement} - Elemento del indicador de escritura.
   */
  createTypingIndicator() {
    const typingIndicator = document.createElement('div');
    typingIndicator.className = 'typing-indicator';
    typingIndicator.innerHTML = `
      <div class="typing-animation">
        <span></span>
        <span></span>
        <span></span>
      </div>
    `;
    return typingIndicator;
  },

  /**
   * Procesa la respuesta del servidor.
   * @param {Object} data - Datos de la respuesta.
   * @param {string} userMessage - Mensaje del usuario.
   * @param {HTMLElement} inputField - Campo de entrada.
   */
  processResponse(data, userMessage, inputField) {
    const chatBoxContent = document.getElementById('chat-box-content');
    console.log('🔍 INICIANDO processResponse');
    console.log('📦 Respuesta del servidor:', data);
    console.log('📊 Tipo de data.response:', typeof data.response);
    console.log('📋 data.response:', data.response);
    
    // Check if response is a string that needs parsing
    if (typeof data.response === 'string') {
      console.log('🔍 Response is string, trying to parse as JSON...');
      try {
        const parsedResponse = JSON.parse(data.response);
        console.log('✅ Parsed response:', parsedResponse);
        data.response = parsedResponse;
      } catch (e) {
        console.log('❌ Not valid JSON, treating as text:', e);
      }
    }

    // Guardar los IDs de los mensajes para referencia
    this.lastUserMessageId = data.user_message_id;
    this.lastBotMessageId = data.bot_message_id;

    // Debug logging for exam processing
    console.log('🔍 Processing response for exam generation');
    
    // Verificar si es una respuesta de examen
    if (data.response && typeof data.response === 'object') {
      console.log('🎯 Tipo de respuesta:', data.response.response_type);
      console.log('🔍 Response object keys:', Object.keys(data.response));
      console.log('🔍 Full response object:', data.response);
      
      // Check if this might be a combined response even without explicit response_type
      let hasExplanation = data.response.explanation;
      let hasStructuredData = data.response.structured_data || data.response.structured_response;
      
      // Check nested data for combined response (common case)
      if (!hasExplanation && !hasStructuredData && data.response.data && typeof data.response.data === 'object') {
        console.log('🔍 Checking nested data for combined response...');
        hasExplanation = data.response.data.explanation;
        hasStructuredData = data.response.data.structured_data || data.response.data.structured_response;
        
        if (hasExplanation && hasStructuredData) {
          console.log('🎯 Found combined response nested in data! Promoting to top level.');
          // Move the nested combined response to the top level
          data.response = data.response.data;
        }
      }
      
      console.log('🔍 Has explanation:', !!hasExplanation);
      console.log('🔍 Has structured data:', !!hasStructuredData);
      
      if (hasExplanation && hasStructuredData && !data.response.response_type) {
        console.log('🔍 Detected combined response without explicit type, setting response_type');
        data.response.response_type = 'combined';
      } else if (hasExplanation && hasStructuredData && data.response.response_type === 'text') {
        console.log('🔍 Detected combined response with wrong type (text), correcting to combined');
        data.response.response_type = 'combined';
      }

      // Manejar respuesta combinada (explicación + examen)
      if (data.response.response_type === 'combined') {
        console.log('Procesando respuesta de tipo combinada');
        console.log('Datos de respuesta combinada:', data.response);
        
        // Primero mostrar la explicación
        if (data.response.explanation) {
          this.createBotMessage(data.response.explanation, chatBoxContent);
        }
        
        // Luego mostrar el examen interactivo
        let structuredData = data.response.structured_data || data.response.structured_response;
        
        // Si no encontramos structured_data directamente, buscar en otros lugares
        if (!structuredData && data.response.data) {
          structuredData = data.response.data;
        }
        
        console.log('Buscando datos estructurados...');
        console.log('structured_data:', data.response.structured_data);
        console.log('structured_response:', data.response.structured_response);  
        console.log('data:', data.response.data);
        console.log('structuredData final:', structuredData);
        
        if (structuredData) {
          console.log('Procesando datos estructurados:', structuredData);
          
          // Asegurar que structuredData tiene el formato correcto para examHandler
          let examData = structuredData;
          if (structuredData.response_type === 'interactive_test' && structuredData.data) {
            examData = structuredData;
          } else if (structuredData.data) {
            examData = {
              response_type: 'interactive_test',
              data: structuredData.data
            };
          } else if (structuredData.title && structuredData.questions) {
            // Los datos del examen están directamente en structuredData
            examData = {
              response_type: 'interactive_test',
              data: structuredData
            };
          }
          
          console.log('Datos de examen normalizados:', examData);
          
          const examElement = this.examHandler.handleExamResponse(examData);
          if (examElement) {
            const botMessage = document.createElement('div');
            botMessage.className = 'chat-message bot-message';
            botMessage.appendChild(examElement);
            chatBoxContent.appendChild(botMessage);

            // Aplicar animación de entrada
            setTimeout(() => {
              botMessage.classList.add('visible');
            }, 100);
          } else {
            console.error('No se pudo crear el elemento de examen en respuesta combinada');
            console.error('Datos recibidos:', structuredData);
            console.error('Datos normalizados:', examData);
            
            // Fallback: mostrar como texto si no se puede crear el examen
            this.createBotMessage('Se generó un examen pero hubo un problema mostrándolo.', chatBoxContent);
          }
        } else {
          console.error('No se encontraron datos estructurados en respuesta combinada');
          console.error('Respuesta completa:', data.response);
          console.error('Claves disponibles:', Object.keys(data.response));
          
          // Fallback: mostrar solo la explicación
          if (data.response.explanation) {
            console.log('Fallback: mostrando solo explicación');
          } else {
            this.createBotMessage('Se generó contenido pero hubo un problema procesándolo.', chatBoxContent);
          }
        }
      }
      // Manejar respuesta de tipo interactive_test
      else if (data.response.response_type === 'interactive_test') {
        console.log('Procesando respuesta de tipo interactive_test');
        // Crear mensaje del bot con el examen
        const examElement = this.examHandler.handleExamResponse(data.response);
        if (examElement) {
          const botMessage = document.createElement('div');
          botMessage.className = 'chat-message bot-message';
          botMessage.appendChild(examElement);
          chatBoxContent.appendChild(botMessage);

          // Aplicar animación de entrada
          setTimeout(() => {
            botMessage.classList.add('visible');
          }, 100);
        } else {
          // Fallback si no se pudo crear el examen
          console.error('No se pudo crear el elemento de examen');
          this.createBotMessage('No se pudo generar el examen. Por favor, intenta de nuevo.', chatBoxContent);
        }
      }
      // Manejar respuesta de tipo test (compatibilidad con versiones anteriores)
      else if (data.response.response_type === 'test') {
        console.log('Procesando respuesta de tipo test');
        // Intentar parsear la respuesta como JSON
        try {
          let examData = data.response.data;

          // Si es una cadena, intentar parsearla como JSON
          if (typeof examData === 'string') {
            // Intentar encontrar un objeto JSON en la respuesta
            const jsonMatch = examData.match(/\{[\s\S]*\}/);

            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              examData = JSON.parse(jsonStr);
            } else {
              throw new Error('No se encontró JSON en la respuesta');
            }
          }

          // Verificar que tenga la estructura esperada
          if (examData.title && examData.questions && Array.isArray(examData.questions)) {
            console.log('Examen JSON válido detectado:', examData.title);

            // Crear objeto de respuesta en formato interactive_test
            const interactiveTestData = {
              response_type: 'interactive_test',
              data: examData
            };

            // Procesar el examen
            const examElement = this.examHandler.handleExamResponse(interactiveTestData);
            if (examElement) {
              const botMessage = document.createElement('div');
              botMessage.className = 'chat-message bot-message';
              botMessage.appendChild(examElement);
              chatBoxContent.appendChild(botMessage);

              // Aplicar animación de entrada
              setTimeout(() => {
                botMessage.classList.add('visible');
              }, 100);
              return; // Salir de la función si se procesó correctamente
            }
          } else {
            throw new Error('JSON no tiene estructura de examen');
          }
        } catch (e) {
          console.error('Error al procesar respuesta de tipo test:', e);
          // Si no se pudo parsear como JSON, mostrar como texto
          this.createBotMessage(data.response.data, chatBoxContent);
        }
      }
      // Manejar respuesta de tipo text
      else if (data.response.response_type === 'text') {
        console.log('📝 Processing text response');
        console.log('📝 Text data:', data.response.data);
        console.log('📝 Text data type:', typeof data.response.data);
        
        // Mensaje de texto normal - handle both string and object cases
        let textContent = data.response.data;
        if (typeof textContent === 'object') {
          console.log('⚠️ Text data is object, converting to string');
          textContent = JSON.stringify(textContent, null, 2);
        }
        this.createBotMessage(textContent, chatBoxContent);
      }
      // Manejar respuesta de tipo imagen
      else if (data.response.response_type === 'image') {
        console.log('Procesando respuesta de tipo imagen');
        // Verificar si el manejador de imágenes está disponible
        if (window.ImageHandler) {
          const imageElement = window.ImageHandler.handleImageResponse(data.response);
          if (imageElement) {
            const botMessage = document.createElement('div');
            botMessage.className = 'chat-message bot-message';
            botMessage.appendChild(imageElement);
            chatBoxContent.appendChild(botMessage);

            // Aplicar animación de entrada
            setTimeout(() => {
              botMessage.classList.add('visible');
            }, 100);
          } else {
            // Fallback si no se pudo crear el elemento de imagen
            this.createBotMessage('No se pudo cargar la imagen generada.', chatBoxContent);
          }
        } else {
          // Si no está disponible el manejador de imágenes, mostrar la URL
          this.createBotMessage(`Imagen generada: ${data.response.data}`, chatBoxContent);
        }
      }
      // Intentar detectar si es un examen en otro formato
      else {
        // Otro tipo de respuesta JSON
        // Intentar detectar si es un examen en formato JSON
        const responseStr = typeof data.response === 'string' ? data.response : JSON.stringify(data.response);
        console.log('Intentando detectar examen en respuesta desconocida:', responseStr.substring(0, 100) + '...');

        try {
          // Intentar parsear como JSON si es una cadena
          const responseObj = typeof data.response === 'string' ? JSON.parse(data.response) : data.response;

          // Verificar si parece un examen (tiene title y questions)
          if (responseObj.title && responseObj.questions && Array.isArray(responseObj.questions)) {
            console.log('Detectado posible examen en formato JSON');
            const examData = {
              response_type: 'interactive_test',
              data: responseObj
            };

            const examElement = this.examHandler.handleExamResponse(examData);
            if (examElement) {
              const botMessage = document.createElement('div');
              botMessage.className = 'chat-message bot-message';
              botMessage.appendChild(examElement);
              chatBoxContent.appendChild(botMessage);

              // Aplicar animación de entrada
              setTimeout(() => {
                botMessage.classList.add('visible');
              }, 100);
              return; // Salir de la función si se procesó correctamente
            }
          }
        } catch (e) {
          console.log('No es un JSON válido o no tiene formato de examen:', e);
        }

        // Si llegamos aquí, mostrar un mensaje de error en lugar del JSON crudo
        console.error('🚨 Formato de respuesta no reconocido:', data.response);
        this.createBotMessage('Lo siento, hubo un problema procesando la respuesta. Por favor, intenta de nuevo.', chatBoxContent);
      }
    } else {
      // Respuesta de texto plano (compatibilidad con versiones anteriores)
      const responseText = data.response || 'No se recibió respuesta del servidor';

      // Intentar detectar si es un examen en formato JSON
      try {
        if (typeof responseText === 'string' && responseText.trim().startsWith('{')) {
          const jsonResponse = JSON.parse(responseText);

          // Verificar si parece un examen (tiene title y questions)
          if (jsonResponse.title && jsonResponse.questions && Array.isArray(jsonResponse.questions)) {
            console.log('Detectado posible examen en formato JSON (texto plano)');
            const examData = {
              response_type: 'interactive_test',
              data: jsonResponse
            };

            const examElement = this.examHandler.handleExamResponse(examData);
            if (examElement) {
              const botMessage = document.createElement('div');
              botMessage.className = 'chat-message bot-message';
              botMessage.appendChild(examElement);
              chatBoxContent.appendChild(botMessage);

              // Aplicar animación de entrada
              setTimeout(() => {
                botMessage.classList.add('visible');
              }, 100);
              return; // Salir de la función si se procesó correctamente
            }
          }
        }
      } catch (e) {
        console.log('No es un JSON válido o no tiene formato de examen:', e);
      }

      this.createBotMessage(responseText, chatBoxContent);
    }
    // El scroll se maneja dentro de createBotMessage

    // Habilitar input
    inputField.disabled = false;
    inputField.focus();

    // Asegurar que el formulario de chat permanezca centrado
    const chatForm = document.querySelector('.chat-form');
    if (chatForm) {
      chatForm.style.margin = '0 auto';
      chatForm.style.left = '0';
      chatForm.style.right = '0';
      chatForm.style.transform = 'none';
    }

    // Actualizar ID del chat si es nuevo
    if (data.chat_id) {
      if (!this.currentChatId) {
        // Usar el título del backend si está disponible
        this.prependChatToSidebar(data.chat_id, data.title || 'New Chat');
      }
      this.currentChatId = data.chat_id;
      localStorage.setItem('activeChatId', data.chat_id);
    }

    // Procesar código y matemáticas
    this.processCodeAndMath();
  },

  /**
   * Procesa bloques de matemáticas en el contenido.
   * Nota: Los bloques de código ahora se procesan con el módulo terminal-code.js
   */
  processCodeAndMath() {
    const chatBoxContent = document.getElementById('chat-box-content');
    const lastMessage = document.querySelector('.chat-message:last-child');

    // Procesar expresiones con <sup> que puedan haber quedado sin procesar
    if (lastMessage) {
      // Buscar elementos con etiquetas <sup> y convertirlos a formato LaTeX
      const supElements = lastMessage.querySelectorAll('sup');
      if (supElements.length > 0) {
        console.log(`Encontrados ${supElements.length} elementos <sup> para convertir`);

        // Convertir cada elemento <sup> a formato LaTeX
        supElements.forEach(supElement => {
          const parent = supElement.parentNode;
          if (parent && parent.textContent) {
            // Obtener el texto completo
            const fullText = parent.innerHTML;
            // Convertir a formato LaTeX
            if (window.convertSupToLatex) {
              const convertedText = window.convertSupToLatex(fullText);
              // Reemplazar el contenido
              parent.innerHTML = convertedText;
            }
          }
        });
      }
    }

    // Procesar matemáticas con MathJax si está disponible
    if (window.MathJax) {
      // Buscar elementos de matemáticas en el último mensaje
      const mathElements = lastMessage ?
        lastMessage.querySelectorAll('.math-inline, .math-block') :
        document.querySelectorAll('.math-inline, .math-block');

      // Registrar para depuración
      if (mathElements.length > 0) {
        console.log(`Procesando ${mathElements.length} elementos matemáticos`);

        // Mostrar los primeros 3 elementos para depuración
        for (let i = 0; i < Math.min(3, mathElements.length); i++) {
          console.log(`Elemento matemático ${i+1}:`, mathElements[i].outerHTML);
        }
      } else {
        // Si no hay elementos matemáticos con clases específicas, buscar expresiones que parezcan matemáticas
        if (lastMessage) {
          const messageContent = lastMessage.querySelector('.message-content');
          if (messageContent && messageContent.innerHTML) {
            const content = messageContent.innerHTML;
            // Buscar patrones que parezcan expresiones matemáticas
            if (content.includes('<sup>') || content.includes('\\int') || content.includes('\\frac') ||
                content.includes('\\sum') || content.includes('\\prod') || content.includes('\\lim')) {
              console.log('Detectadas posibles expresiones matemáticas sin procesar. Intentando convertir...');

              // Intentar convertir expresiones con <sup> a formato LaTeX
              let newContent = content.replace(/([a-zA-Z0-9])\s*<sup>([^<]+)<\/sup>/g,
                '<span class="math-inline">$$$1^{$2}$$</span>');

              // Intentar convertir integrales a formato LaTeX
              newContent = newContent.replace(/\b(\\?int|∫)\s*([^\s<]+)\s*dx\b/g,
                '<span class="math-inline">$$$&$$</span>');

              // Actualizar el contenido
              messageContent.innerHTML = newContent;
            }
          }
        }
      }

      // Procesar todos los elementos matemáticos
      MathJax.typesetPromise()
        .then(() => {
          console.log('MathJax: Procesamiento completado con éxito');
          // Scroll después de que MathJax termine de renderizar
          if (lastMessage) {
            this.forceScrollToElement(lastMessage);
          }
        })
        .catch(err => {
          console.error('MathJax error:', err);
          // Intentar scroll incluso si hay error
          if (lastMessage) {
            this.forceScrollToElement(lastMessage);
          }

          // Intento de recuperación: volver a procesar solo los elementos matemáticos
          if (mathElements.length > 0 && window.retypeset) {
            console.log('Intentando reprocesar matemáticas...');
            setTimeout(window.retypeset, 500);
          }
        });
    }

    // Observar cambios en los bloques de código para scroll después de renderizado
    this.observeCodeBlockRendering();

    // No hacemos scroll automático cuando el bot responde
    // setTimeout(() => {
    //   if (lastMessage) {
    //     this.forceScrollToElement(lastMessage);
    //   }
    // }, 1000);
  },

  /**
   * Observa la renderización de bloques de código para hacer scroll después.
   */
  observeCodeBlockRendering() {
    // Crear un observador para detectar cuando se terminan de renderizar los bloques de código
    const codeObserver = new MutationObserver((mutations) => {
      let hasCodeChanges = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Verificar si se agregaron bloques de código
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE &&
                (node.classList.contains('code-terminal') ||
                 node.querySelector('.code-terminal'))) {
              hasCodeChanges = true;
            }
          });
        }
      });

      // No hacemos scroll automático cuando el bot responde
      // if (hasCodeChanges) {
      //   // Hacer scroll después de que se renderizaron los bloques de código
      //   const lastMessage = document.querySelector('.chat-message:last-child');
      //   if (lastMessage) {
      //     this.forceScrollToElement(lastMessage);
      //   }
      // }
    });

    // Iniciar observación en el documento completo para capturar todos los cambios
    codeObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Detener la observación después de 5 segundos para evitar problemas de rendimiento
    setTimeout(() => {
      codeObserver.disconnect();
    }, 5000);
  },

  /**
   * Maneja la primera interacción del usuario.
   */
  handleFirstMessage() {
    this.isFirstMessage = false;

    // Restaurar posición normal del formulario
    const chatForm = document.querySelector('.chat-form');
    if (chatForm) {
      chatForm.classList.remove('centered-vertically');
    }
  },

  /**
   * Actualiza la UI para el chat actual.
   */
  handleChatUpdates() {
    // Implementación futura para actualizaciones de UI específicas del chat
  },

  /**
   * Alterna la visibilidad del cuadro de entrada.
   */
  toggleInputBox() {
    // Implementación futura para alternar la visibilidad
  },

  /**
   * Crea un elemento de mensaje del usuario.
   * @param {string} message - Mensaje del usuario.
   * @returns {HTMLElement} - Elemento del mensaje del usuario.
   */
  createUserMessageElement(message) {
    const userMessage = document.createElement('div');
    userMessage.className = 'chat-message user-message';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = message;

    userMessage.appendChild(messageContent);
    return userMessage;
  },

  /**
   * Crea un mensaje del usuario en la UI.
   * @param {string} message - Mensaje del usuario.
   * @param {HTMLElement} container - Contenedor donde agregar el mensaje.
   */
  createUserMessage(message, container) {
    const userMessage = this.createUserMessageElement(message);
    container.appendChild(userMessage);

    // Aplicar animación de entrada
    setTimeout(() => {
      userMessage.classList.add('visible');

      // Forzar scroll al mensaje recién creado
      this.forceScrollToElement(userMessage);
    }, 10);
  },

  /**
   * Crea un mensaje del bot en la UI.
   * @param {string} message - Mensaje del bot.
   * @param {HTMLElement} container - Contenedor donde agregar el mensaje.
   */
  createBotMessage(message, container) {
    const botMessage = this.createBotMessageElement(message);

    // Añadir el ID del mensaje si está disponible
    if (this.lastBotMessageId) {
      botMessage.dataset.messageId = this.lastBotMessageId;
    }

    container.appendChild(botMessage);

    // Aplicar animación de entrada
    setTimeout(() => {
      botMessage.classList.add('visible');

      // No hacemos scroll automático cuando el bot responde
      // this.forceScrollToElement(botMessage);
    }, 100);
  },

  /**
   * Crea un mensaje de error del bot en la UI.
   * @param {string} errorMessage - Mensaje de error.
   * @param {HTMLElement} container - Contenedor donde agregar el mensaje.
   */
  createBotErrorMessage(errorMessage, container) {
    const botMessage = document.createElement('div');
    botMessage.className = 'chat-message bot-message error-message';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = errorMessage;

    botMessage.appendChild(messageContent);
    container.appendChild(botMessage);
  },

  /**
   * Crea un elemento de mensaje del bot.
   * @param {string} messageContent - Contenido del mensaje.
   * @returns {HTMLElement} - Elemento del mensaje.
   */
  createBotMessageElement(messageContent) {
    const botMessage = document.createElement('div');
    botMessage.className = 'chat-message bot-message';

    // Buscar contenido <think> en el mensaje
    const thinkMatch = messageContent.match(/<think>([\s\S]*?)<\/think>/i);
    let thinkContent = '';
    let mainContent = messageContent;

    // Si hay contenido <think>, extraerlo y procesarlo
    if (thinkMatch) {
      thinkContent = thinkMatch[1];
      mainContent = messageContent.replace(thinkMatch[0], '');
      this.addThinkContent(botMessage, thinkContent);
    }

    // Detectar si el contenido es un examen interactivo antes de aplicar lógica de longitud
    let isInteractiveTest = false;
    try {
      // Verificar si el contenido principal parece ser JSON de un examen
      if (typeof mainContent === 'string' && mainContent.trim().startsWith('{')) {
        const jsonResponse = JSON.parse(mainContent.trim());

        // Verificar si tiene la estructura de un examen
        if (jsonResponse.title && jsonResponse.questions && Array.isArray(jsonResponse.questions)) {
          console.log('Detectado examen interactivo en createBotMessageElement:', jsonResponse.title);
          isInteractiveTest = true;

          // Intentar renderizar como examen interactivo
          const examData = {
            response_type: 'interactive_test',
            data: jsonResponse
          };

          const examElement = this.examHandler.handleExamResponse(examData);
          if (examElement) {
            botMessage.appendChild(examElement);
            return botMessage; // Retornar directamente el examen renderizado
          }
        }
      }
    } catch (e) {
      // Si no se puede parsear como JSON, continuar con el procesamiento normal
      console.log('Contenido no es JSON válido de examen, procesando como texto normal');
    }

    // Formatear el contenido principal con soporte para Markdown, LaTeX y código
    const formattedMainContent = this.formatMessage(mainContent);

    // Crear el contenedor principal
    const mainContentDiv = document.createElement('div');
    mainContentDiv.className = 'message-content';
    mainContentDiv.innerHTML = formattedMainContent;

    botMessage.appendChild(mainContentDiv);

    // Determinar si el mensaje es corto (sin contar el contenido de think)
    // Definimos un mensaje corto como aquel con menos de 100 caracteres
    // NOTA: Los exámenes interactivos no aplican esta lógica de longitud
    const isShortMessage = !isInteractiveTest && mainContent.trim().length < 100;

    // No mostrar footer para mensajes cortos o exámenes interactivos
    if (!isShortMessage && !isInteractiveTest) {
      // Agregar footer con botones de acción solo para mensajes largos que no sean exámenes
      const footerDiv = document.createElement('div');
      footerDiv.className = 'message-footer';

    // Botón de copiar
    const copyButton = document.createElement('button');
    copyButton.className = 'action-button copy-button';
    copyButton.innerHTML = '<i class="fas fa-copy"></i>';
    copyButton.title = 'Copiar mensaje';
    copyButton.addEventListener('click', () => {
      navigator.clipboard.writeText(messageContent)
        .then(() => {
          copyButton.innerHTML = '<i class="fas fa-check"></i>';
          setTimeout(() => {
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
          }, 2000);
        })
        .catch(err => console.error('Error al copiar:', err));
    });



    // Botón de guardar en notebook
    const bookmarkButton = document.createElement('button');
    bookmarkButton.className = 'action-button bookmark-button';
    bookmarkButton.innerHTML = '<i class="fas fa-bookmark"></i>';
    bookmarkButton.title = 'Guardar en notebook';
    bookmarkButton.addEventListener('click', () => {
      // Obtener la instancia del notebook
      const notebook = window.notebook || {};
      if (notebook.saveMessageToNotebook) {
        // Guardar el mensaje en el notebook
        notebook.saveMessageToNotebook(mainContentDiv);
      } else {
        console.error('Notebook no está disponible');
      }
    });

    // Botón de eliminar mensaje
    const deleteButton = document.createElement('button');
    deleteButton.className = 'action-button delete-button';
    deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
    deleteButton.title = 'Eliminar mensaje';
    deleteButton.addEventListener('click', async () => {
      try {
        // Obtener el ID del mensaje (necesitamos añadir esto al elemento)
        const messageId = botMessage.dataset.messageId;

        if (!messageId) {
          console.error('No se pudo encontrar el ID del mensaje');
          return;
        }

        // Enviar solicitud para eliminar el mensaje
        const response = await fetch(`/chat/delete_message/${messageId}/`, {
          method: 'DELETE',
          headers: {
            'X-CSRFToken': ChatBot.csrftoken,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Error al eliminar el mensaje');
        }

        const data = await response.json();

        if (data.status === 'success') {
          // Si el backend también eliminó un mensaje de usuario, buscarlo y eliminarlo de la UI
          if (data.deleted_user_message) {
            // Buscar el mensaje de usuario que precede al mensaje del bot
            // Primero guardamos una referencia al elemento anterior antes de eliminar el mensaje del bot
            const previousElement = botMessage.previousElementSibling;

            // Verificar si el elemento anterior es un mensaje de usuario y eliminarlo
            if (previousElement && previousElement.classList.contains('user-message')) {
              previousElement.remove();
            } else {
              // Si no encontramos el mensaje de usuario directamente antes, buscar todos los mensajes de usuario
              // y eliminar el último que se creó antes que el mensaje del bot
              const chatBoxContent = document.getElementById('chat-box-content');
              if (chatBoxContent) {
                const userMessages = chatBoxContent.querySelectorAll('.user-message');
                if (userMessages.length > 0) {
                  // Eliminar el último mensaje de usuario (asumiendo que es el que precedió al mensaje del bot)
                  userMessages[userMessages.length - 1].remove();
                }
              }
            }
          }

          // Eliminar el mensaje del bot de la UI
          botMessage.remove();

          // Si el chat fue eliminado en el backend, actualizar la UI
          if (data.chat_deleted && data.chat_id) {
            console.log(`Chat ${data.chat_id} eliminado por no tener mensajes`);

            // Eliminar el chat de la barra lateral
            const chatElement = document.querySelector(`.chat-instance[data-chat-id="${data.chat_id}"]`);
            if (chatElement && chatElement.parentNode) {
              chatElement.parentNode.removeChild(chatElement);
            }

            // Si era el chat activo, mostrar estado inicial
            if (this.currentChatId === data.chat_id) {
              this.currentChatId = null;
              localStorage.removeItem('activeChatId');
              this.displayInitialState();
            }
          }

          // Mostrar notificación
          if (window.notebook && window.notebook.showNotification) {
            window.notebook.showNotification('Mensaje eliminado correctamente');
          }
        } else {
          throw new Error(data.message || 'Error desconocido');
        }      } catch (error) {
        console.error('Error eliminando mensaje:', error);
        this.showConfirmationDialog(`No se pudo eliminar el mensaje: ${error.message}`, {
          confirmText: 'Entendido',
          showCancel: false,
          isError: true
        });
      }
    });

    footerDiv.appendChild(copyButton);
    footerDiv.appendChild(bookmarkButton);
    footerDiv.appendChild(deleteButton);
    botMessage.appendChild(footerDiv);
    } // Cierre del if (!isShortMessage && !isInteractiveTest)

    return botMessage;
  },

  /**
   * Formatea un mensaje con soporte para Markdown, LaTeX y código.
   * @param {string} text - Texto a formatear.
   * @returns {string} - HTML formateado.
   */
  formatMessage(text) {
    if (!text) return '';

    // Usar el nuevo formateador para procesar el texto
    // Este maneja automáticamente bloques de código, matemáticas y Markdown
    return formatter.format(text);
  },

  // El método formatMarkdown ha sido reemplazado por el formatter.js

  /**
   * Agrega contenido de pensamiento al mensaje del bot con un header "Think..."
   * @param {HTMLElement} botMessage - Elemento del mensaje.
   * @param {string} thinkContent - Contenido del pensamiento.
   * @param {Object} [options] - Opciones de personalización (opcional).
   */
  addThinkContent(botMessage, thinkContent, options = {}) {
    const thinkContainer = document.createElement('div');
    thinkContainer.className = 'think-container';

    const thinkHeader = document.createElement('div');
    thinkHeader.className = 'think-header';
    thinkHeader.textContent = 'Think...';

    // Formatear el contenido del pensamiento usando el mismo formateador que los mensajes normales
    const formattedThinkContent = this.formatMessage(thinkContent);

    const thinkBubble = document.createElement('div');
    thinkBubble.className = 'think-bubble';
    thinkBubble.innerHTML = formattedThinkContent; // Usar innerHTML para permitir formato

    const toggleButton = document.createElement('button');
    toggleButton.className = 'think-toggle';
    toggleButton.setAttribute('aria-label', 'Toggle thought content');
    toggleButton.innerHTML = '<i class="fa-solid fa-chevron-down"></i>';

    const mistOverlay = document.createElement('div');
    mistOverlay.className = 'mist-overlay';

    // Hacer que el header también pueda expandir/colapsar el contenido
    const toggleExpansion = function() {
      const isExpanded = thinkContainer.classList.toggle('expanded');
      const icon = toggleButton.querySelector('i');

      icon.classList.toggle('fa-chevron-down');
      icon.classList.toggle('fa-chevron-up');

      if (isExpanded) {
        thinkBubble.style.maxHeight = '200px';
        thinkBubble.classList.add('scrollable');
        mistOverlay.classList.add('hidden');
        thinkHeader.classList.add('expanded');
      } else {
        thinkBubble.style.maxHeight = '0';
        thinkBubble.classList.remove('scrollable');
        mistOverlay.classList.remove('hidden');
        thinkHeader.classList.remove('expanded');
      }

      // Procesar matemáticas si MathJax está disponible
      if (window.MathJax) {
        // Procesar expresiones con <sup> que puedan haber quedado sin procesar
        const supElements = thinkBubble.querySelectorAll('sup');
        if (supElements.length > 0) {
          console.log(`Encontrados ${supElements.length} elementos <sup> en contenido de pensamiento`);

          // Convertir cada elemento <sup> a formato LaTeX
          supElements.forEach(supElement => {
            const parent = supElement.parentNode;
            if (parent && parent.textContent) {
              // Obtener el texto completo
              const fullText = parent.innerHTML;
              // Convertir a formato LaTeX
              if (window.convertSupToLatex) {
                const convertedText = window.convertSupToLatex(fullText);
                // Reemplazar el contenido
                parent.innerHTML = convertedText;
              }
            }
          });
        }

        // Buscar elementos de matemáticas en el contenido de pensamiento
        const mathElements = thinkBubble.querySelectorAll('.math-inline, .math-block');

        if (mathElements.length > 0) {
          console.log(`Procesando ${mathElements.length} elementos matemáticos en contenido de pensamiento`);
        } else {
          // Si no hay elementos matemáticos con clases específicas, buscar expresiones que parezcan matemáticas
          const content = thinkBubble.innerHTML;
          if (content.includes('<sup>') || content.includes('\\int') || content.includes('\\frac') ||
              content.includes('\\sum') || content.includes('\\prod') || content.includes('\\lim')) {
            console.log('Detectadas posibles expresiones matemáticas sin procesar en contenido de pensamiento');

            // Intentar convertir expresiones con <sup> a formato LaTeX
            let newContent = content.replace(/([a-zA-Z0-9])\s*<sup>([^<]+)<\/sup>/g,
              '<span class="math-inline">$$$1^{$2}$$</span>');

            // Intentar convertir integrales a formato LaTeX
            newContent = newContent.replace(/\b(\\?int|∫)\s*([^\s<]+)\s*dx\b/g,
              '<span class="math-inline">$$$&$$</span>');

            // Actualizar el contenido
            thinkBubble.innerHTML = newContent;
          }
        }

        // Procesar matemáticas en el contenido de pensamiento
        MathJax.typesetPromise([thinkBubble])
          .catch(err => {
            console.error('Error al procesar matemáticas en contenido de pensamiento:', err);
            // Intento de recuperación
            if (window.retypeset) {
              setTimeout(() => window.retypeset(), 500);
            }
          });
      }
    };

    // Evitar que el clic en el botón de toggle se propague al header
    toggleButton.addEventListener('click', (event) => {
      event.stopPropagation();
      toggleExpansion();
    });

    // El header también puede expandir/colapsar el contenido
    thinkHeader.addEventListener('click', toggleExpansion);

    // Agregar el botón de toggle al header en lugar de al contenedor principal
    thinkHeader.appendChild(toggleButton);

    thinkContainer.appendChild(thinkHeader);
    thinkContainer.appendChild(thinkBubble);
    thinkContainer.appendChild(mistOverlay);
    botMessage.appendChild(thinkContainer);

    // Aplicar tema claro/oscuro
    const isDarkTheme = document.body.classList.contains('dark-theme');
    const isLightTheme = document.body.classList.contains('light-theme');

    if (isLightTheme) {
      thinkContainer.classList.add('light-theme');
    }

    if (options.theme) {
      thinkContainer.classList.add(`theme-${options.theme}`);
    }

    // Procesar código y matemáticas dentro del contenido de pensamiento
    setTimeout(() => {
      if (window.escapeHtmlInCodeBlocks) {
        window.escapeHtmlInCodeBlocks();
      }

      if (window.MathJax) {
        // Procesar expresiones con <sup> que puedan haber quedado sin procesar
        const supElements = thinkBubble.querySelectorAll('sup');
        if (supElements.length > 0) {
          console.log(`Encontrados ${supElements.length} elementos <sup> en contenido de pensamiento (setTimeout)`);

          // Convertir cada elemento <sup> a formato LaTeX
          supElements.forEach(supElement => {
            const parent = supElement.parentNode;
            if (parent && parent.textContent) {
              // Obtener el texto completo
              const fullText = parent.innerHTML;
              // Convertir a formato LaTeX
              if (window.convertSupToLatex) {
                const convertedText = window.convertSupToLatex(fullText);
                // Reemplazar el contenido
                parent.innerHTML = convertedText;
              }
            }
          });
        }

        // Buscar elementos de matemáticas en el contenido de pensamiento
        const mathElements = thinkBubble.querySelectorAll('.math-inline, .math-block');

        if (mathElements.length > 0) {
          console.log(`Procesando ${mathElements.length} elementos matemáticos en contenido de pensamiento (setTimeout)`);
        } else {
          // Si no hay elementos matemáticos con clases específicas, buscar expresiones que parezcan matemáticas
          const content = thinkBubble.innerHTML;
          if (content.includes('<sup>') || content.includes('\\int') || content.includes('\\frac') ||
              content.includes('\\sum') || content.includes('\\prod') || content.includes('\\lim')) {
            console.log('Detectadas posibles expresiones matemáticas sin procesar en contenido de pensamiento (setTimeout)');

            // Intentar convertir expresiones con <sup> a formato LaTeX
            let newContent = content.replace(/([a-zA-Z0-9])\s*<sup>([^<]+)<\/sup>/g,
              '<span class="math-inline">$$$1^{$2}$$</span>');

            // Intentar convertir integrales a formato LaTeX
            newContent = newContent.replace(/\b(\\?int|∫)\s*([^\s<]+)\s*dx\b/g,
              '<span class="math-inline">$$$&$$</span>');

            // Actualizar el contenido
            thinkBubble.innerHTML = newContent;
          }
        }

        // Procesar matemáticas en el contenido de pensamiento
        MathJax.typesetPromise([thinkBubble])
          .catch(err => {
            console.error('Error al procesar matemáticas en contenido de pensamiento (setTimeout):', err);
            // Intento de recuperación
            if (window.retypeset) {
              setTimeout(() => window.retypeset(), 500);
            }
          });
      }
    }, 100);

    // Establecer altura inicial (completamente colapsado)
    thinkBubble.style.maxHeight = '0';
  },

  /**
   * Desplaza la vista al último mensaje de forma forzada.
   * @param {HTMLElement} container - Contenedor de mensajes.
   * @param {boolean} force - Si es true, fuerza el scroll incluso si el usuario está leyendo mensajes antiguos.
   */
  scrollToBottom(container, force = false) {
    // Obtener el último mensaje
    const lastMessage = document.querySelector('.chat-message:last-child');
    if (!lastMessage) return;

    // Detectar si el usuario está leyendo mensajes antiguos
    const isScrolledToBottom = this.isUserAtBottom();

    // No hacer scroll si el usuario está leyendo mensajes antiguos, a menos que force sea true
    if (!isScrolledToBottom && !force) return;

    // Forzar scroll al último mensaje
    this.forceScrollToElement(lastMessage);
  },

  /**
   * Verifica si el usuario está cerca del final de la página.
   * @returns {boolean} - True si el usuario está cerca del final.
   */
  isUserAtBottom() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    const windowHeight = window.innerHeight;

    // Use more accurate height calculation to prevent extra space issues
    const documentHeight = document.documentElement.scrollHeight;

    // More precise threshold - consider at bottom if within 100px
    const threshold = 100;
    const distanceFromBottom = documentHeight - scrollPosition - windowHeight;

    return distanceFromBottom <= threshold;
  },

  /**
   * Fuerza el scroll a un elemento específico.
   * @param {HTMLElement} element - Elemento al que hacer scroll.
   */
  forceScrollToElement(element) {
    if (!element) return;

    try {
      // Calculate precise scroll position to prevent extra space
      const chatBox = document.querySelector('.chat-box');
      const chatBoxPaddingBottom = chatBox ? parseInt(getComputedStyle(chatBox).paddingBottom) : 120;

      // Calculate target position accounting for bottom padding
      const elementRect = element.getBoundingClientRect();
      const elementTop = element.offsetTop;
      const elementHeight = element.offsetHeight;
      const windowHeight = window.innerHeight;

      // Position the element so it's visible with proper spacing
      const targetPosition = elementTop + elementHeight - windowHeight + chatBoxPaddingBottom - 20;

      window.scrollTo({
        top: Math.max(0, targetPosition),
        behavior: 'smooth'
      });
    } catch (error) {
      console.error('Error al hacer scroll:', error);
      // Fallback to simple scrollIntoView
      try {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } catch (fallbackError) {
        console.error('Fallback scroll error:', fallbackError);
      }
    }
  },

  /**
   * Método legacy para compatibilidad con código existente.
   * @param {HTMLElement} container - Contenedor de mensajes.
   */
  scrollToNewMessage(container) {
    this.scrollToBottom(container, true);
  },

  /**
   * Carga un chat existente.
   * @param {string} chatId - ID del chat a cargar.
   */
  async loadChat(chatId) {
    try {
      // Actualizar UI
      this.currentChatId = chatId;
      localStorage.setItem('activeChatId', chatId);

      const chatBoxContent = document.getElementById('chat-box-content');
      if (!chatBoxContent) return;

      chatBoxContent.innerHTML = '';

      // Restaurar posición normal del formulario
      const chatForm = document.querySelector('.chat-form');
      if (chatForm) {
        chatForm.classList.remove('centered-vertically');
      }

      // Resaltar chat activo en la barra lateral
      document.querySelectorAll('.chat-instance').forEach(chat => {
        chat.classList.remove('active');
      });

      const activeChat = document.querySelector(`.chat-instance[data-chat-id="${chatId}"]`);
      if (activeChat) {
        activeChat.classList.add('active');
      }

      // Cargar mensajes del chat
      const response = await fetch(`/chat/load_chat_messages/${chatId}/`);
      if (!response.ok) throw new Error('Error cargando mensajes');

      const data = await response.json();

      if (data.messages && data.messages.length > 0) {
        this.isFirstMessage = false;

        // Crear mensajes en la UI
        data.messages.forEach(msg => {
          if (msg.sender === 'user') {
            const userMessage = this.createUserMessageElement(msg.content);
            chatBoxContent.appendChild(userMessage);
          } else {
            // Intentar parsear el contenido para ver si es un examen
            try {
              const parsedContent = JSON.parse(msg.content);

              // Verificar si es una respuesta de examen
              if (parsedContent.response_type === 'interactive_test') {
                // Determinar si el examen ya fue calificado (asumimos que sí para exámenes guardados)
                const isPreGraded = true;

                // Crear mensaje del bot con el examen
                const examData = parsedContent.data;

                // Guardar las respuestas correctas en el objeto examData para usarlas al calificar
                if (!examData.savedCorrectAnswers) {
                  examData.savedCorrectAnswers = {};
                  examData.questions.forEach(question => {
                    examData.savedCorrectAnswers[question.id] = question.correct_answer_index;
                  });
                }

                const examElement = this.examHandler.renderExam(examData, isPreGraded);

                if (examElement) {
                  const botMessage = document.createElement('div');
                  botMessage.className = 'chat-message bot-message';
                  botMessage.dataset.messageId = msg.id;
                  botMessage.appendChild(examElement);
                  chatBoxContent.appendChild(botMessage);

                  // Aplicar animación de entrada
                  setTimeout(() => {
                    botMessage.classList.add('visible');
                  }, 100);
                } else {
                  // Fallback si no se pudo crear el examen
                  const botMessage = this.createBotMessageElement('No se pudo cargar el examen.');
                  botMessage.dataset.messageId = msg.id;
                  chatBoxContent.appendChild(botMessage);
                }
              } else if (parsedContent.response_type === 'text') {
                // Mensaje de texto normal
                const botMessage = this.createBotMessageElement(parsedContent.data);
                botMessage.dataset.messageId = msg.id;
                chatBoxContent.appendChild(botMessage);
              } else if (parsedContent.response_type === 'image') {
                // Respuesta de tipo imagen
                if (window.ImageHandler) {
                  const imageElement = window.ImageHandler.handleImageResponse(parsedContent);
                  if (imageElement) {
                    const botMessage = document.createElement('div');
                    botMessage.className = 'chat-message bot-message';
                    botMessage.dataset.messageId = msg.id;
                    botMessage.appendChild(imageElement);
                    chatBoxContent.appendChild(botMessage);

                    // Aplicar animación de entrada
                    setTimeout(() => {
                      botMessage.classList.add('visible');
                    }, 100);
                  } else {
                    // Fallback si no se pudo crear el elemento de imagen
                    const botMessage = this.createBotMessageElement('No se pudo cargar la imagen generada.');
                    botMessage.dataset.messageId = msg.id;
                    chatBoxContent.appendChild(botMessage);
                  }
                } else {
                  // Si no está disponible el manejador de imágenes, mostrar la URL
                  const botMessage = this.createBotMessageElement(`Imagen generada: ${parsedContent.data}`);
                  botMessage.dataset.messageId = msg.id;
                  chatBoxContent.appendChild(botMessage);
                }
              } else {
                // Otro tipo de respuesta JSON
                const botMessage = this.createBotMessageElement(msg.content);
                botMessage.dataset.messageId = msg.id;
                chatBoxContent.appendChild(botMessage);
              }
            } catch (e) {
              // No es JSON, tratar como mensaje normal
              const botMessage = this.createBotMessageElement(msg.content);
              botMessage.dataset.messageId = msg.id;
              chatBoxContent.appendChild(botMessage);
            }
          }
        });

        // Procesar código y matemáticas
        this.processCodeAndMath();

        // Desplazar al último mensaje
        const lastMessage = document.querySelector('.chat-message:last-child');
        if (lastMessage) {
          this.forceScrollToElement(lastMessage);
        }

        // Enfocar el campo de entrada
        const userInput = document.getElementById('user-input');
        if (userInput) userInput.focus();
      } else {
        // Si no hay mensajes, mostrar estado inicial
        this.displayInitialState();
      }    } catch (error) {
      console.error('Error cargando chat:', error);
      this.showConfirmationDialog(`No se pudo cargar el chat: ${error.message}`, {
        confirmText: 'Entendido',
        showCancel: false,
        isError: true
      });
    }
  },
  /**
   * Elimina un chat.
   * @param {Event} event - Evento del clic.
   * @param {string} chatId - ID del chat a eliminar.
   */
  async deleteChat(event, chatId) {
    event.stopPropagation();

    // Confirmar la acción con el usuario usando el diálogo personalizado
    const confirmation = await this.showConfirmationDialog(
      '¿Eliminar chat?',
      '¿Estás seguro de que quieres eliminar este chat? Esta acción no se puede deshacer.',
      {
        confirmText: 'Eliminar',
        cancelText: 'Cancelar'
      }
    );

    if (!confirmation) {
      return;
    }

    try {
      const response = await fetch(`/chat/delete_chat/${chatId}/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': this.csrftoken,
        },
      });

      if (!response.ok) throw new Error('Error eliminando chat');

      // Eliminar chat de la UI
      const chatElement = document.querySelector(`.chat-instance[data-chat-id="${chatId}"]`);
      if (chatElement && chatElement.parentNode) {
        chatElement.parentNode.removeChild(chatElement);
      }

      // Si era el chat activo, mostrar estado inicial
      if (this.currentChatId === chatId) {
        this.newChat();
      }
    } catch (error) {
      console.error('Error eliminando chat:', error);

      // Mostrar error con diálogo personalizado
      await this.showConfirmationDialog(
        'Error',
        `No se pudo eliminar el chat: ${error.message}`,
        {
          confirmText: 'Entendido',
          showCancel: false,
          isError: true
        }
      );
    }
  },

  /**
   * Agrega un chat a la barra lateral.
   * @param {string} chatId - ID del chat.
   * @param {string} title - Título del chat.
   */
  prependChatToSidebar(chatId, title) {
    const chatContainer = document.querySelector('.chats-container');
    if (!chatContainer) {
      console.error('No se encontró el contenedor de chats');
      return;
    }

    // Crear elemento de chat
    const chatElement = document.createElement('div');
    chatElement.className = 'chat-instance';
    chatElement.setAttribute('data-chat-id', chatId);
    chatElement.addEventListener('click', () => this.loadChat(chatId));

    // Formatear fecha actual
    const now = new Date();
    const formattedDate = now.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });

    // Crear contenido del chat
    chatElement.innerHTML = `
      <div class="chat-instance-header">
        <div class="chat-icon">
          <i class='bx bx-conversation'></i>
        </div>
        <div class="chat-info">
          <p>${title}</p>
          <span class="chat-date">${formattedDate}</span>
        </div>
        <div class="chat-actions">
          <button class="rename-chat-btn">
            <i class="bx bx-edit-alt"></i>
          </button>
          <button class="delete-chat-btn">
            <i class="bx bx-trash-alt"></i>
          </button>
        </div>
      </div>
    `;

    // Configurar botón de renombrar
    const renameButton = chatElement.querySelector('.rename-chat-btn');
    if (renameButton) {
      renameButton.addEventListener('click', (e) => this.renameChat(e, chatId));
    }

    // Configurar botón de eliminar
    const deleteButton = chatElement.querySelector('.delete-chat-btn');
    if (deleteButton) {
      deleteButton.addEventListener('click', (e) => this.deleteChat(e, chatId));
    }

    // Agregar al inicio del contenedor
    if (chatContainer.firstChild) {
      chatContainer.insertBefore(chatElement, chatContainer.firstChild);
    } else {
      chatContainer.appendChild(chatElement);
    }
  },

  /**
   * Limpia el chat actual.
   */
  clearChat() {
    const chatBoxContent = document.getElementById('chat-box-content');
    if (chatBoxContent) {
      chatBoxContent.innerHTML = '';
    }
    this.isFirstMessage = true;
  },

  /**
   * Renombra un chat.
   * @param {Event} event - Evento del clic.
   * @param {string} chatId - ID del chat a renombrar.
   */
  async renameChat(event, chatId) {
    event.stopPropagation();

    // Obtener el elemento del chat
    const chatElement = document.querySelector(`.chat-instance[data-chat-id="${chatId}"]`);
    if (!chatElement) return;

    // Obtener el elemento del título
    const titleElement = chatElement.querySelector('.chat-info p');
    if (!titleElement) return;

    // Guardar el título actual
    const currentTitle = titleElement.textContent;

    // Crear campo de entrada para el nuevo título
    const inputElement = document.createElement('input');
    inputElement.type = 'text';
    inputElement.className = 'chat-rename-input';
    inputElement.value = currentTitle;
    inputElement.maxLength = 50;

    // Reemplazar el título con el campo de entrada
    titleElement.style.display = 'none';
    titleElement.parentNode.insertBefore(inputElement, titleElement);

    // Enfocar el campo de entrada
    inputElement.focus();
    inputElement.select();

    // Función para guardar el nuevo título
    const saveNewTitle = async () => {
      const newTitle = inputElement.value.trim();

      // Validar que el título no esté vacío
      if (!newTitle) {
        inputElement.value = currentTitle;
        return;
      }

      // Restaurar la visualización del título
      titleElement.style.display = '';
      inputElement.remove();

      // Actualizar el título en la UI temporalmente
      titleElement.textContent = newTitle;

      try {
        // Enviar la solicitud al servidor
        const response = await fetch(`/chat/update_chat_title/${chatId}/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': this.csrftoken,
          },
          body: JSON.stringify({ title: newTitle }),
        });

        const data = await response.json();        if (data.status !== 'success') {
          // Si hay un error, restaurar el título anterior
          titleElement.textContent = currentTitle;
          this.showConfirmationDialog(`Error al renombrar el chat: ${data.message}`, {
            confirmText: 'Entendido',
            showCancel: false,
            isError: true
          });
        }      } catch (error) {
        console.error('Error renombrando chat:', error);
        // Restaurar el título anterior en caso de error
        titleElement.textContent = currentTitle;
        this.showConfirmationDialog(`No se pudo renombrar el chat: ${error.message}`, {
          confirmText: 'Entendido',
          showCancel: false,
          isError: true
        });
      }
    };

    // Guardar al presionar Enter o al perder el foco
    inputElement.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        saveNewTitle();
      } else if (e.key === 'Escape') {
        e.preventDefault();
        titleElement.style.display = '';
        inputElement.remove();
      }
    });

    inputElement.addEventListener('blur', saveNewTitle);
  },  /**
   * Muestra un diálogo de confirmación personalizado
   */
  showConfirmationDialog(title, message, options = {}) {
    return new Promise((resolve) => {
      const {
        confirmText = 'Confirmar',
        cancelText = 'Cancelar',
        isError = false,
        showCancel = true
      } = options;

      // Prevenir scroll del body
      document.body.classList.add('confirmation-open');

      // Crear el diálogo de confirmación
      const dialog = document.createElement('div');
      dialog.className = 'confirmation-dialog';

      const iconClass = isError ? 'bx-x-circle' : 'bx-error';
      const iconColor = isError ? 'var(--danger-color)' : 'var(--warning-color)';

      dialog.innerHTML = `
        <div class="confirmation-content">
          <div class="confirmation-header">
            <div class="confirmation-icon" style="color: ${iconColor}; background-color: rgba(${isError ? 'var(--danger-color-rgb)' : 'var(--warning-color-rgb)'}, 0.1);">
              <i class="bx ${iconClass}"></i>
            </div>
            <h4>${title}</h4>
          </div>
          <div class="confirmation-body">
            <p>${message}</p>
          </div>
          <div class="confirmation-actions">
            ${showCancel ? `<button class="confirmation-btn cancel">${cancelText}</button>` : ''}
            <button class="confirmation-btn confirm ${isError ? 'error' : ''}">${confirmText}</button>
          </div>
        </div>
      `;

      // Agregar al DOM
      document.body.appendChild(dialog);

      // Aplicar el tema actual
      const currentTheme = document.documentElement.classList.contains('light-theme') ? 'light-theme' : 'dark-theme';
      dialog.classList.add(currentTheme);

      // Mostrar el diálogo con animación
      requestAnimationFrame(() => {
        dialog.classList.add('active');
      });

      // Manejar eventos de los botones
      const cancelBtn = dialog.querySelector('.cancel');
      const confirmBtn = dialog.querySelector('.confirm');

      const cleanup = (result = false) => {
        document.body.classList.remove('confirmation-open');
        dialog.classList.remove('active');
        setTimeout(() => {
          if (dialog.parentNode) {
            document.body.removeChild(dialog);
          }
        }, 400);
        resolve(result);
      };

      if (cancelBtn) {
        cancelBtn.addEventListener('click', () => cleanup(false));
      }

      confirmBtn.addEventListener('click', () => cleanup(true));

      // Cerrar con escape
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          cleanup(false);
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);

      // Cerrar clickeando fuera del diálogo (solo si no es error)
      if (!isError) {
        dialog.addEventListener('click', (e) => {
          if (e.target === dialog) {
            cleanup(false);
          }
        });
      }
    });
  },

  /**
   * Elimina todos los chats del usuario.
   */
  async deleteAllChats() {
    // Confirmar la acción con el usuario usando el diálogo personalizado
    const confirmation = await this.showConfirmationDialog(
      '¿Eliminar todos los chats?',
      '¿Estás seguro de que quieres eliminar todos los chats? Esta acción no se puede deshacer.',
      {
        confirmText: 'Eliminar',
        cancelText: 'Cancelar'
      }
    );

    if (!confirmation) {
      return;
    }

    try {
      const response = await fetch('/chat/delete_all_chats/', {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': this.csrftoken,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) throw new Error('Error eliminando todos los chats');

      const data = await response.json();

      if (data.status === 'success') {
        // Limpiar la barra lateral
        const chatsContainer = document.querySelector('.chats-container');
        if (chatsContainer) {
          chatsContainer.innerHTML = '';
        }

        // Limpiar el chat actual y mostrar estado inicial
        this.currentChatId = null;
        localStorage.removeItem('activeChatId');
        this.clearChat();
        this.displayInitialState();

        // Mostrar notificación si está disponible
        if (window.notebook && window.notebook.showNotification) {
          window.notebook.showNotification('Todos los chats han sido eliminados');
        }
      } else {
        throw new Error(data.message || 'Error desconocido');
      }
    } catch (error) {
      console.error('Error eliminando todos los chats:', error);

      // Mostrar error con diálogo personalizado
      await this.showConfirmationDialog(
        'Error',
        `No se pudieron eliminar todos los chats: ${error.message}`,
        {
          confirmText: 'Entendido',
          showCancel: false,
          isError: true
        }
      );
    }
  }
};

export default ChatBot;
