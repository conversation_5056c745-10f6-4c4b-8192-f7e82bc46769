{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <title><PERSON><PERSON><PERSON></title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Main CSS file that imports all other CSS files -->
    <link rel="stylesheet" href="{% static 'chat/css/main.css' %}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRA<PERSON>@24,400,0,0&icon_names=delete_sweep" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Font Awesome Brand Icons para lenguajes de programación -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/brands.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap" rel="stylesheet">
</head>
<body>
    <!-- UI Frame - Container for all fixed UI elements -->
    <div class="ui-frame">
        <!-- Model indicator in top left -->
        <div class="model-indicator {{ active_provider }}">{{ model_name }}</div>

        <!-- Top Right Menu with theme switcher, new chat button, and profile -->
        <div class="top-right-menu vertical-menu">
            <div class="profile-container" title="Profile">
                <i class='bx bx-user profile-icon' id="profile-icon"></i>
                <div class="profile-menu" id="profile-menu">
                    <a href="#"><i class='bx bx-user'></i> Perfil</a>
                    <a href="#"><i class='bx bx-cog'></i> Configuración</a>
                    <a href="#"><i class='bx bx-info-circle'></i> Acerca de</a>
                    <a href="{% url 'logout' %}"><i class='bx bx-log-out'></i> Cerrar Sesión</a>
                </div>
            </div>
            <button class="action-button" id="theme-toggle-btn" title="Toggle Theme">
                <i class='bx bx-sun'></i>
            </button>
            <button class="action-button" id="notebook-btn" title="Notebook">
                <i class='bx bx-bookmarks'></i>
            </button>
            <button class="action-button" id="notes-btn" title="Notes">
                <i class='bx bx-note'></i>
            </button>
            <button class="action-button" id="new-chat-btn" title="New Chat">
                <i class='bx bx-message-add'></i>
            </button>
            <button class="action-button" id="toggle-graph-btn" title="Toggle Graph Response">
                <i class='bx bx-toggle-left'></i>
            </button>
        </div>

        <!-- Chat Navigation Buttons -->
        <div class="chat-nav-buttons">
            <button id="go-to-top-btn" class="chat-nav-button">
                <i class="fa-solid fa-arrow-up"></i>
            </button>
            <button id="go-to-bottom-btn" class="chat-nav-button">
                <i class="fa-solid fa-arrow-down"></i>
            </button>
        </div>
    </div>

    <!-- Hover Area for Sidebar - Must be immediately before sidebar -->
    <div class="hover-area" id="hover-area"></div>    <!-- Sidebar - Must be immediately after hover-area for CSS hover to work -->
    <div class="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <h3 class="sidebar-title">Chat History</h3>
        </div>

        <!-- Sidebar Content - Chat instances with proper flex layout -->
        <div class="sidebar-content">
            <div class="chats-container">
                {% for chat in user_chats %}
                    <div class="chat-instance" data-chat-id="{{ chat.chat_id }}" onclick="ChatBot.loadChat('{{ chat.chat_id }}')">
                        <div class="chat-instance-header">
                            <div class="chat-icon">
                                <i class='bx bx-conversation'></i>
                            </div>
                            <div class="chat-info">
                                <p>{{ chat.title }}</p>
                                <span class="chat-date">{{ chat.created_at|date:"d M Y" }}</span>
                            </div>
                            <div class="chat-actions">
                                <button class="rename-chat-btn" onclick="ChatBot.renameChat(event, '{{ chat.chat_id }}')">
                                    <i class='bx bx-edit-alt'></i>
                                </button>
                                <button class="delete-chat-btn" onclick="ChatBot.deleteChat(event, '{{ chat.chat_id }}')">
                                    <i class='bx bx-trash-alt' ></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Sidebar Footer with Delete All button -->
        <div class="sidebar-footer">
            <button class="delete-all-btn" onclick="ChatBot.deleteAllChats()" title="Delete All Chats">
                <i class='bx bx-trash'></i>
                <span>Delete All Chats</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="chat-section" id="chat-section">
            <div class="chat-container">
                <div class="chat-box" id="chat-box">
                    <div class="chat-box-content" id="chat-box-content">
                        <!-- Messages will be inserted here dynamically -->
                    </div>
                </div>

                <form method="POST" class="chat-form">
                    {% csrf_token %}
                    <div class="input-box">
                        <div class="input-container">
                            <button type="button" class="input-icon" id="attach-chat-btn">
                                <i class='bx bx-paperclip'></i>
                            </button>
                            <textarea id="user-input" rows="1" maxlength="100000" spellcheck="false"></textarea>
                            <button type="button" class="input-icon" id="voice-input-btn">
                                <i class='bx bx-microphone' ></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- Scripts -->
    <!-- Fix for deprecation warnings -->
    <script src="{% static 'chat/js/fix-deprecation.js' %}"></script>
    <script src="{% static 'chat/js/sidebar.js' %}"></script>
    <script>
        // Pass username to JavaScript
        window.username = "{{ username }}";

        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true,
            processEnvironments: true,
            packages: ['base', 'ams', 'noerrors', 'noundefined']
          },
          options: {
            skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
            ignoreHtmlClass: 'tex2jax_ignore',
            processHtmlClass: 'tex2jax_process'
          },
          svg: {
            fontCache: 'global'
          },
          startup: {
            typeset: true,
            ready: () => {
              console.log('MathJax is ready');
              MathJax.startup.defaultReady();
            }
          }
        };

        // Función auxiliar para volver a procesar matemáticas cuando sea necesario
        window.retypeset = function() {
          if (window.MathJax) {
            console.log('Reprocesando matemáticas...');
            MathJax.typesetPromise().catch(err => console.error('Error al reprocesar matemáticas:', err));
          }
        };

        // Función para convertir notación HTML de superíndices a LaTeX
        window.convertSupToLatex = function(text) {
          return text.replace(/([a-zA-Z0-9])\s*<sup>([^<]+)<\/sup>/g, '$1^{$2}');
        };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <!-- Markdown-it para parsing de Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-table@2.0.4/dist/markdown-it-table.min.js"></script>
    <!-- DOMPurify para sanitización de HTML -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    <!-- highlight.js for advanced syntax highlighting - VS Code theme -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/vs2015.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <!-- Additional languages for highlight.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/typescript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/json.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/bash.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/sql.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/csharp.min.js"></script>
    <!-- Cargar el script principal con soporte para módulos ES6 -->
    <script type="module" src="{% static 'chat/js/main.js' %}"></script>
</body>
</html>