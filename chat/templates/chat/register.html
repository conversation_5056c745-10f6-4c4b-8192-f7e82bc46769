{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Regístrate en EduChat - Tu espacio de trabajo de IA unificado">
    <title>Registro - EduChat</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'chat/css/landing.css' %}">
</head>
<body class="auth-page">
    <!-- Header -->
    <header>
        <nav>
            <a href="{% url 'login' %}" class="logo">EduChat</a>
            <ul class="nav-links">
                <li><a href="#features">Características</a></li>
                <li><a href="#pricing">Precios</a></li>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#about">Acerca de</a></li>
                <li><a href="#support">Soporte</a></li>
            </ul>
            <div class="nav-cta">
                <a href="{% url 'login' %}" class="btn btn-ghost">¿Ya tienes cuenta?</a>
                <a href="{% url 'login' %}" class="btn btn-primary">Iniciar Sesión</a>
            </div>
        </nav>
    </header>

    <!-- Contenido Principal -->
    <section class="auth-section">
        <div class="registration-layout">
            <!-- Main Registration Form -->
            <div class="auth-container">
                <h2>Crear Cuenta</h2>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="POST">
                    {% csrf_token %}

                    <!-- Username Field -->
                    <p>
                        <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <ul class="errorlist">
                                {% for error in form.username.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <!-- Password Field -->
                    <p>
                        <label for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <ul class="errorlist">
                                {% for error in form.password1.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <!-- Password Confirmation Field -->
                    <p>
                        <label for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <ul class="errorlist">
                                {% for error in form.password2.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </p>

                    <button type="submit" class="auth-button">Registrarme</button>
                </form>
                <p class="auth-link">¿Ya tienes cuenta? <a href="{% url 'login' %}">Inicia sesión aquí</a></p>
            </div>

            <!-- Help Text Container -->
            <div class="help-text-container">
                <h3>Guía Rápida</h3>

                <div class="help-section">
                    <h4>Usuario</h4>
                    <ul>
                        <li>Máximo 150 caracteres</li>
                        <li>Letras, números y @/./+/-/_</li>
                        <li>Debe ser único</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>Contraseña</h4>
                    <ul>
                        <li>Mínimo 8 caracteres</li>
                        <li>No solo números</li>
                        <li>No información personal</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Header background change on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
