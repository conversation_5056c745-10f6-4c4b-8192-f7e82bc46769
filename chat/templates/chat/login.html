{% load static %}
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Inicia sesión en EduChat - Tu espacio de trabajo de IA unificado">
    <title>Iniciar Sesión - EduChat</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'chat/css/landing.css' %}">
</head>
<body class="auth-page">
    <!-- Header -->
    <header>
        <nav>
            <a href="{% url 'login' %}" class="logo">EduChat</a>
            <ul class="nav-links">
                <li><a href="#features">Características</a></li>
                <li><a href="#pricing">Precios</a></li>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#about">Acerca de</a></li>
                <li><a href="#support">Soporte</a></li>
            </ul>
            <div class="nav-cta">
                <a href="{% url 'register' %}" class="btn btn-ghost">¿No tienes cuenta?</a>
                <a href="{% url 'register' %}" class="btn btn-primary">Registrarse</a>
            </div>
        </nav>
    </header>

    <!-- Contenido Principal -->
    <section class="auth-section">
        <div class="auth-container">
            <h2>Iniciar Sesión</h2>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="POST">
                {% csrf_token %}

                <!-- Username Field -->
                <p>
                    <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <ul class="errorlist">
                            {% for error in form.username.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </p>

                <!-- Password Field -->
                <p>
                    <label for="{{ form.password.id_for_label }}">{{ form.password.label }}</label>
                    {{ form.password }}
                    {% if form.password.errors %}
                        <ul class="errorlist">
                            {% for error in form.password.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </p>

                <button type="submit" class="auth-button">Iniciar Sesión</button>
            </form>
            <p class="auth-link">¿No tienes cuenta? <a href="{% url 'register' %}">Regístrate aquí</a></p>
        </div>
    </section>

    <script>
        // Header background change on scroll
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
