version: '3.8'

services:
  # Standalone MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: chat_agent_mysql_standalone
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: std_db
      MYSQL_USER: develorian
      MYSQL_PASSWORD: vr41nr0t410
      MYSQL_ROOT_PASSWORD: root_password_123
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_standalone_data:/var/lib/mysql
      - ./mysql/my.cnf:/etc/mysql/conf.d/my.cnf:ro
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
      - ./mysql/backups:/backups
    networks:
      - mysql_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 10s
      retries: 5
      interval: 10s
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password

  # Optional: phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: chat_agent_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password_123
      MYSQL_ROOT_PASSWORD: root_password_123
    ports:
      - "8080:80"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - mysql_network
    profiles:
      - with-admin

  # MySQL backup service
  mysql-backup:
    image: mysql:8.0
    container_name: chat_agent_mysql_backup
    restart: "no"
    environment:
      MYSQL_HOST: mysql
      MYSQL_USER: root
      MYSQL_PASSWORD: root_password_123
      MYSQL_DATABASE: std_db
    volumes:
      - ./mysql/backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - mysql_network
    profiles:
      - backup
    command: ["/backup.sh"]

# Named volumes for persistent data
volumes:
  mysql_standalone_data:
    driver: local

# Network for database communication
networks:
  mysql_network:
    driver: bridge 