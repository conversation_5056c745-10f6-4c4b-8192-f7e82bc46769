# Development Dockerfile
FROM python:3.11-slim

# Set environment variables for development
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONPATH=/app

# Set work directory
WORKDIR /app

# Install system dependencies including development tools
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        gcc \
        g++ \
        default-libmysqlclient-dev \
        pkg-config \
        curl \
        wget \
        netcat-openbsd \
        git \
        vim \
        htop \
        procps \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt /app/
RUN pip install --no-cache-dir --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt \
    && pip install --no-cache-dir \
        django-debug-toolbar \
        django-extensions \
        ipython \
        jupyter

# Create directories
RUN mkdir -p /app/logs /app/media /app/staticfiles

# Create development entrypoint script
COPY docker-entrypoint-dev.sh /app/
RUN chmod +x /app/docker-entrypoint-dev.sh

# Expose port
EXPOSE 8000

# Set entrypoint for development
ENTRYPOINT ["/app/docker-entrypoint-dev.sh"] 