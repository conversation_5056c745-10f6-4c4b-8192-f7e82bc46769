#!/bin/bash

# Chat Agent Docker Startup Script
# Provides convenient commands for managing the Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE} Chat Agent Docker Manager${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to check if .env exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found!"
        if [ -f env.example ]; then
            print_status "Copying env.example to .env..."
            cp env.example .env
            print_warning "Please edit .env with your actual API keys before starting!"
            return 1
        else
            print_error "env.example not found. Please create .env manually."
            return 1
        fi
    fi
    return 0
}

# Function to show help
show_help() {
    print_header
    echo "Usage: ./start.sh [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  up, start           Start the application (production)"
    echo "  dev                 Start development environment"
    echo "  down, stop          Stop all services"
    echo "  restart             Restart all services"
    echo "  logs                Show application logs"
    echo "  db-only             Start only MySQL database"
    echo "  build               Build the Docker images"
    echo "  clean               Clean up containers and volumes"
    echo "  status              Show service status"
    echo "  shell               Open Django shell"
    echo "  migrate             Run database migrations"
    echo "  backup              Create database backup"
    echo "  help                Show this help message"
    echo ""
    echo "Options:"
    echo "  --with-nginx        Include Nginx reverse proxy"
    echo "  --with-admin        Include phpMyAdmin (for db-only)"
    echo "  --with-redis        Include Redis (development)"
    echo "  --with-email        Include Mailhog (development)"
    echo ""
    echo "Examples:"
    echo "  ./start.sh up                    # Start production"
    echo "  ./start.sh dev --with-redis      # Start dev with Redis"
    echo "  ./start.sh db-only --with-admin  # Start DB with phpMyAdmin"
}

# Function to start production environment
start_production() {
    print_status "Starting Chat Agent in production mode..."
    
    if [[ "$*" == *"--with-nginx"* ]]; then
        print_status "Including Nginx reverse proxy..."
        docker-compose --profile production up -d
    else
        docker-compose up -d
    fi
    
    print_status "Waiting for services to be ready..."
    sleep 10
    
    print_status "✅ Production environment started!"
    echo ""
    echo "Access points:"
    echo "  🌐 Web App: http://localhost:8000"
    echo "  🔧 Admin: http://localhost:8000/admin/ (admin/admin123)"
    if [[ "$*" == *"--with-nginx"* ]]; then
        echo "  🚀 Nginx: http://localhost"
    fi
}

# Function to start development environment
start_development() {
    print_status "Starting Chat Agent in development mode..."
    
    local profiles=""
    if [[ "$*" == *"--with-redis"* ]]; then
        profiles="$profiles --profile with-redis"
        print_status "Including Redis..."
    fi
    if [[ "$*" == *"--with-email"* ]]; then
        profiles="$profiles --profile with-email"
        print_status "Including Mailhog..."
    fi
    
    docker-compose -f docker-compose.dev.yml $profiles up -d
    
    print_status "Waiting for services to be ready..."
    sleep 10
    
    print_status "✅ Development environment started!"
    echo ""
    echo "Access points:"
    echo "  🌐 Web App: http://localhost:8001"
    echo "  🔧 Admin: http://localhost:8001/admin/ (dev/dev123)"
    if [[ "$*" == *"--with-email"* ]]; then
        echo "  📧 Mailhog: http://localhost:8025"
    fi
}

# Function to start database only
start_database() {
    print_status "Starting MySQL database only..."
    
    if [[ "$*" == *"--with-admin"* ]]; then
        print_status "Including phpMyAdmin..."
        docker-compose -f docker-compose.db-only.yml --profile with-admin up -d
        echo "  🗄️ phpMyAdmin: http://localhost:8080"
    else
        docker-compose -f docker-compose.db-only.yml up -d
    fi
    
    print_status "✅ Database started!"
    echo ""
    echo "Connection details:"
    echo "  Host: localhost"
    echo "  Port: 3306"
    echo "  Database: std_db"
    echo "  User: develorian"
}

# Function to stop services
stop_services() {
    print_status "Stopping all Chat Agent services..."
    docker-compose down
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.db-only.yml down
    print_status "✅ All services stopped!"
}

# Function to show logs
show_logs() {
    print_status "Showing application logs..."
    if docker-compose ps | grep -q "chat_agent_web"; then
        docker-compose logs -f web
    elif docker-compose -f docker-compose.dev.yml ps | grep -q "chat_agent_web_dev"; then
        docker-compose -f docker-compose.dev.yml logs -f web
    else
        print_error "No running web service found!"
        return 1
    fi
}

# Function to show status
show_status() {
    print_header
    print_status "Service Status:"
    echo ""
    
    # Check production
    if docker-compose ps | grep -q "Up"; then
        echo "📦 Production Services:"
        docker-compose ps
        echo ""
    fi
    
    # Check development
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
        echo "🛠️ Development Services:"
        docker-compose -f docker-compose.dev.yml ps
        echo ""
    fi
    
    # Check database only
    if docker-compose -f docker-compose.db-only.yml ps | grep -q "Up"; then
        echo "🗄️ Database Services:"
        docker-compose -f docker-compose.db-only.yml ps
        echo ""
    fi
    
    # Show resource usage
    if docker ps | grep -q "chat_agent"; then
        echo "💻 Resource Usage:"
        docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep chat_agent
    fi
}

# Main script logic
case "$1" in
    "up"|"start")
        check_env_file || exit 1
        start_production "$@"
        ;;
    "dev")
        check_env_file || exit 1
        start_development "$@"
        ;;
    "down"|"stop")
        stop_services
        ;;
    "restart")
        stop_services
        sleep 3
        check_env_file || exit 1
        start_production "$@"
        ;;
    "logs")
        show_logs
        ;;
    "db-only")
        start_database "$@"
        ;;
    "build")
        print_status "Building Docker images..."
        docker-compose build
        docker-compose -f docker-compose.dev.yml build
        print_status "✅ Build complete!"
        ;;
    "clean")
        print_warning "This will remove all containers and volumes!"
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            stop_services
            docker system prune -f
            docker volume prune -f
            print_status "✅ Cleanup complete!"
        fi
        ;;
    "status")
        show_status
        ;;
    "shell")
        if docker-compose ps | grep -q "chat_agent_web.*Up"; then
            docker-compose exec web python manage.py shell
        elif docker-compose -f docker-compose.dev.yml ps | grep -q "chat_agent_web_dev.*Up"; then
            docker-compose -f docker-compose.dev.yml exec web python manage.py shell
        else
            print_error "No running web service found!"
        fi
        ;;
    "migrate")
        if docker-compose ps | grep -q "chat_agent_web.*Up"; then
            docker-compose exec web python manage.py migrate
        elif docker-compose -f docker-compose.dev.yml ps | grep -q "chat_agent_web_dev.*Up"; then
            docker-compose -f docker-compose.dev.yml exec web python manage.py migrate
        else
            print_error "No running web service found!"
        fi
        ;;
    "backup")
        timestamp=$(date +%Y%m%d_%H%M%S)
        backup_file="backup_${timestamp}.sql"
        print_status "Creating database backup: $backup_file"
        
        if docker-compose ps | grep -q "chat_agent_mysql.*Up"; then
            docker-compose exec db mysqldump -u root -p'root_password_123' std_db > "$backup_file"
        elif docker-compose -f docker-compose.dev.yml ps | grep -q "chat_agent_mysql_dev.*Up"; then
            docker-compose -f docker-compose.dev.yml exec db mysqldump -u root -p'root_password_123' std_db_dev > "$backup_file"
        else
            print_error "No running database found!"
            exit 1
        fi
        
        print_status "✅ Backup created: $backup_file"
        ;;
    "help"|"--help"|"-h"|"")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac 