#!/bin/bash

# Exit on any error
set -e

echo "Starting Chat Agent Application..."

# Function to wait for MySQL
wait_for_mysql() {
    echo "Waiting for MySQL to be ready..."
    while ! nc -z "$DB_HOST" "$DB_PORT"; do
        echo "MySQL is unavailable - sleeping"
        sleep 2
    done
    echo "MySQL is up - continuing..."
}

# Wait for database if DB_HOST is set
if [ "$DB_HOST" ]; then
    wait_for_mysql
fi

# Run database migrations
echo "Running database migrations..."
python manage.py makemigrations --noinput
python manage.py migrate --noinput

# Create superuser if it doesn't exist
echo "Checking for superuser..."
python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print("Superuser 'admin' created with password 'admin123'")
else:
    print("Superuser 'admin' already exists")
EOF

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Start the server
echo "Starting Django development server..."
exec python manage.py runserver 0.0.0.0:8000 